# MMKV 批量搜索工具使用说明

## 概述

这是一个高性能的 MMKV 批量搜索工具，可以在大量 tgz 压缩文件中搜索 MMKV 数据。支持模糊搜索、多关键字搜索、加密文件处理、智能线程数、临时文件管理、子命令、调试模式等功能。

## 命令行语法

### 主搜索命令
```bash
mmkvreader [OPTIONS] [--search <SEARCH_TERMS>] <DIRECTORY>
```

### 子命令
```bash
mmkvreader clean <DIRECTORY>              # 清理临时文件
mmkvreader zip <DIRECTORY>                # 压缩JSON文件
mmkvreader extract [--force] <DIRECTORY>  # 解压所有tgz文件
mmkvreader test                           # 测试MMKV文件加载
```

## 参数说明

### 必需参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `<DIRECTORY>` | tgz 文件所在目录或单个tgz文件路径 | `"E:\data\tgz_files"` 或 `"E:\data\file.tgz"` |

### 可选参数

| 参数 | 短参数 | 默认值 | 说明 |
|------|--------|--------|------|
| `--search <SEARCH_TERMS>` | `-s` | 无（全量导出） | 搜索关键字，支持多个关键字 |
| `--key <CRYPT_KEY>` | `-k` | `fd9f5bef68c54a1ecf70757a6d6f565b` | 加密密钥 |
| `--mode <SEARCH_MODE>` | `-m` | `key` | **搜索模式：`key`（默认）或 `value`** |
| `--zip <COMPRESS>` | `-z` | `yes` | 是否压缩结果：`yes` 或 `no` |
| `--threads <THREADS>` | `-t` | `CPU数-1` | 线程数（最大1024） |
| `--cleanup <CLEANUP>` | `-c` | `no` | 是否清理临时文件：`yes` 或 `no` |
| `--debug` | 无 | `false` | 启用调试模式，显示详细信息 |


## 使用示例

### 1. 全量导出模式（无搜索关键字）

```bash
# 导出所有key和value（不指定搜索关键字）
mmkvreader "E:\data\tgz_files"

# 单文件全量导出
mmkvreader "E:\data\file.tgz"
```

### 2. 基本搜索

```bash
# 在指定目录搜索包含 "config" 的 key
mmkvreader "E:\data\tgz_files" --search "config"

# 单文件搜索
mmkvreader "E:\data\file.tgz" --search "config"
```

### 3. 多关键字搜索

```bash
# 支持多种分隔符：中文逗号、英文逗号、空格、竖杠
mmkvreader "E:\data\tgz_files" --search "config,camera|user data"
```

### 4. 在 value 中搜索

```bash
# 在 value 内容中搜索
mmkvreader "E:\data\tgz_files" --search "camera" --mode value
```

### 5. 自定义加密密钥

```bash
# 使用自定义加密密钥
mmkvreader "E:\data\tgz_files" --search "config" --key "your_custom_key"
```

### 6. 不压缩结果

```bash
# 保留单独的 JSON 文件，不压缩
mmkvreader "E:\data\tgz_files" --search "config" --zip no
```

### 7. 自定义线程数

```bash
# 使用8个线程进行并行处理（默认CPU数-1）
mmkvreader "E:\data\tgz_files" --search "config" --threads 8
```

### 8. 清理临时文件

```bash
# 处理完成后自动清理解压目录和JSON文件
mmkvreader "E:\data\tgz_files" --search "config" --cleanup yes
```

### 9. 调试模式

```bash
# 启用调试模式，显示详细的调试信息
mmkvreader "E:\data\tgz_files" --search "config" --debug
```

### 10. 子命令使用

```bash
# 清理临时文件（解压目录和JSON文件）
mmkvreader clean "E:\data\tgz_files"

# 压缩所有JSON文件为ZIP
mmkvreader zip "E:\data\tgz_files"

# 解压所有tgz文件
mmkvreader extract "E:\data\tgz_files"

# 强制覆盖解压（删除已存在的解压目录）
mmkvreader extract --force "E:\data\tgz_files"

# 测试MMKV文件加载
mmkvreader test
```

### 11. 完整示例

```bash
# 完整的搜索命令（智能线程数）
mmkvreader "E:\360MoveData\Users\LiShixi\Desktop\CK20240714" \
  --search "token,advertiser_id,gifshow_,pro_auth_info,devicelD,userlD,passToken,api_st,kwtk,summonArrowConfig,enableShowRocketP" \
  --mode value \
  --key "fd9f5bef68c54a1ecf70757a6d6f565b" \
  --cleanup no \
  --zip yes \
  --debug
```

## 搜索关键字分隔符

支持以下分隔符来分割多个关键字：

| 分隔符 | 说明 | 示例 |
|--------|------|------|
| `,` | 英文逗号 | `"config,camera"` |
| `，` | 中文逗号 | `"config，camera"` |
| ` ` | 空格 | `"config camera"` |
| `|` | 竖杠 | `"config|camera"` |

可以混合使用：`"config，camera|user data"`

## 搜索模式

### 搜索模式详解

#### 1. 全量导出模式（无搜索关键字）
当不指定 `--search` 参数时，工具会导出所有的 key-value 对：

```bash
# 导出所有数据
mmkvreader "E:\data\tgz_files"
```

#### 2. 默认搜索模式
- **默认模式**: `key` - 在 MMKV 的 key 名称中搜索
- **可选模式**: `value` - 在 MMKV 的 value 内容中搜索

#### 3. 模糊搜索
所有搜索都支持模糊搜索，**不区分大小写**。

**示例：**
- 搜索 `"TOKEN"` 可以匹配 `"token"`、`"Token"`、`"ACCESS_TOKEN"` 等
- 搜索 `"user"` 可以匹配 `"User"`、`"USER_ID"`、`"current_user"` 等

#### 4. Key 模式 (`--mode key`)
在 MMKV 的 key 名称中进行模糊搜索。

**示例：**
```bash
mmkvreader "E:\data" --search "USER_ID" --mode key
```
会找到名为 `user_id`、`User_ID_Cache`、`current_user_id` 等的 key。

#### 5. Value 模式 (`--mode value`)
在 MMKV 的 value 内容中进行模糊搜索。

**示例：**
```bash
mmkvreader "E:\data" --search "CAMERA" --mode value
```
会找到 value 中包含 "camera"、"Camera"、"CAMERA_SETTINGS" 等字符串的所有 key-value 对。

## 输出结果

### 控制台输出

#### 批量搜索模式
程序会实时显示处理进度：

```
🔍 MMKV 批量搜索工具
==================================================
📁 目录: E:\data\tgz_files
🔑 加密密钥: fd9f5bef68c54a1ecf70757a6d6f565b
🔍 搜索关键字: ["config", "camera"]
📋 搜索模式: Value
📦 压缩结果: 是
🧵 线程数: 15 (CPU数: 16)
🧹 清理临时文件: 否
🐛 调试模式: 关闭

🚀 使用优化的多关键字搜索模式
🔍 关键字列表: ["config", "camera"]
📦 找到 1500 个 tgz 文件
⏳ 进度: 100/1500 (6.7%), 速度: 12.5 文件/秒, 总匹配: 45
...
✅ 多关键字搜索完成: 处理 1500 文件，找到 156 匹配

==================================================
🎉 所有搜索任务完成!
📊 总体统计:
   搜索关键字: 2 个
   总匹配数: 156
   总耗时: 98.45 秒
📦 结果已压缩到: E:\data\tgz_files\ck_search_results_20241224_143022.zip
```

#### 单文件搜索模式
```
🔍 MMKV 单文件搜索工具
==================================================
📄 文件: E:\data\example.tgz
🔑 加密密钥: fd9f5bef68c54a1ecf70757a6d6f565b
🔍 搜索关键字: ["config"]
📋 搜索模式: Key
📦 压缩结果: 是
🧹 清理临时文件: 否
🐛 调试模式: 关闭

🚀 使用单文件搜索模式
🔍 关键字列表: ["config"]
✅ 单文件搜索完成: 找到 5 匹配

==================================================
🎉 单文件搜索任务完成!
📊 统计:
   搜索关键字: 1 个
   总匹配数: 5
   处理的MMKV文件数: 3
   总耗时: 1.23 秒
📦 结果已压缩到: E:\data\ck_search_results_20241224_143022.zip
```

#### 全量导出模式
```
🔍 MMKV 批量搜索工具
==================================================
📁 目录: E:\data\tgz_files
🔑 加密密钥: fd9f5bef68c54a1ecf70757a6d6f565b
🔍 搜索关键字: <空> (返回所有key和value)
📋 搜索模式: Key
📦 压缩结果: 是

🚀 使用全量导出模式
📋 导出所有key和value
📦 找到 100 个 tgz 文件
⏳ 进度: 50/100 (50.0%), 速度: 8.5 文件/秒, 总匹配: 12450
...
✅ 全量导出完成: 处理 100 文件，导出 25000 条记录
```

### 结果文件

#### 压缩模式 (`--zip yes`)
- 生成一个 ZIP 文件：`ck_search_results_<时间戳>.zip`
- 包含所有匹配的 JSON 结果文件
- 包含搜索摘要文件 `search_summary.txt`
- 自动清理临时 JSON 文件

#### 非压缩模式 (`--zip no`)
- 为每个有匹配的 tgz 文件生成对应的 JSON 文件
- 文件名格式：`<tgz文件名>.json`

### 文件命名规则

#### 解压目录
- **命名规则**: `<tgz文件名>/` （直接使用tgz文件名，不添加后缀）
- **示例**: `example.tgz` → `example/`

#### JSON结果文件
- **命名规则**: `<tgz文件名>.json` （与tgz文件名相同）
- **示例**: `example.tgz` → `example.json`
- **内容**: 包含所有搜索关键字的匹配结果

#### ZIP压缩文件
- **命名规则**: `ck_search_results_<时间戳>.zip`
- **时间戳格式**: `YYYYMMDD_HHMMSS`（本地时间）
- **示例**: `ck_search_results_20241224_143022.zip`

### JSON 结果格式

```json
{
  "tgz_file": "E:\\data\\example.tgz",
  "processed_at": "2024-01-15T10:30:45Z",
  "total_mmkv_files": 3,
  "total_matches": 2,
  "processing_time_ms": 1250,
  "matches": [
    {
      "mmkv_file": "data/user_config",
      "mmkv_id": "user_config",
      "key": "camera_settings",
      "value": "{\"resolution\":\"1920x1080\",\"fps\":30}",
      "value_type": "string",
      "match_type": "value",
      "matched_keywords": ["camera", "settings"]
    }
  ],
  "errors": []
}
```

## 高级功能

### 智能线程数控制
- **默认线程数**: CPU数-1（智能适配硬件）
- **最大线程数**: 1024
- **自动限制**: 超过1024会自动限制为1024，0会设置为CPU数-1
- **性能优化**: 每个线程处理一个tgz文件，避免资源竞争
- **硬件适配**: 自动检测CPU核心数，预留1个核心给系统

### 搜索目录优化
- **默认行为**: 直接在解压根目录中搜索MMKV文件
- **无需指定**: 解压后的根目录就是MMKV文件所在目录
- **性能提升**: 直接在根目录搜索，避免递归遍历
- **文件过滤**: 自动跳过 `.crc`、`.log`、`.tmp` 等非MMKV文件

### 调试模式功能
- **启用方式**: 使用 `--debug` 参数
- **调试信息**: 显示每个MMKV文件的详细信息
- **包含内容**:
  - 文件名和all_keys数量
  - 搜索关键字列表
  - 前10个keys（如果总数超过10个）
  - 搜索结果数量
- **性能影响**: 调试模式会增加输出，可能影响处理速度

### 子命令功能
- **clean命令**: 清理解压目录和JSON文件，保留ZIP文件
- **zip命令**: 将所有JSON文件压缩为ZIP，可选择删除原文件
- **extract命令**: 解压所有tgz文件，支持强制覆盖模式
- **test命令**: 测试MMKV文件加载和读取功能
- **智能识别**: 自动识别临时文件，不会误删重要文件

### 临时文件管理
- **解压目录**: 每个tgz文件解压到与文件名相同的目录（不添加后缀）
- **JSON结果**: 每个tgz文件生成与文件名相同的JSON结果文件
- **多关键字结果**: 所有搜索关键字的结果合并到同一个JSON文件中
- **清理选项**:
  - `--cleanup no` (默认): 保留解压目录和JSON文件，便于调试
  - `--cleanup yes`: 自动清理解压目录，节省磁盘空间

### 单文件处理支持
- **输入类型**: 支持目录和单个tgz文件
- **自动检测**: 根据输入路径自动选择处理模式
- **功能一致**: 单文件模式支持所有搜索功能

### ZIP文件命名
- **固定格式**: `ck_search_results_时间戳.zip`
- **时间戳格式**: `YYYYMMDD_HHMMSS`（本地时间）
- **示例**: `ck_search_results_20241224_143022.zip`

## 性能特点

- **智能并行处理**: 默认CPU数-1线程，可设置1-1024个线程
- **直接目录搜索**: 在解压根目录直接搜索MMKV文件
- **文件类型过滤**: 自动跳过 `.crc`、`.log`、`.tmp` 等文件
- **模糊搜索**: 不区分大小写，提高匹配率
- **高效内存**: 流式处理，支持处理 20,000+ 文件
- **实时进度条**: 显示处理进度、速度、匹配数量
- **错误容错**: 单文件失败不影响整体处理
- **简化文件管理**: 解压目录和JSON文件直接使用tgz文件名
- **多模式支持**: 支持目录批量处理和单文件处理
- **全量导出**: 支持无搜索关键字的全量数据导出
- **优化搜索**: 一次文件打开搜索所有关键字，减少I/O开销
- **调试支持**: 详细的调试信息帮助问题排查
- **子命令支持**: 独立的清理、压缩、解压和测试功能

## 常见问题

### 1. 目录不存在
```
❌ 错误: 目录不存在: E:\nonexistent
```
**解决**: 检查目录路径是否正确

### 2. 没有找到 tgz 文件
```
❌ 目录中没有找到 tgz 文件
```
**解决**: 确认目录中包含 .tgz 或 .tar.gz 文件

### 3. 加密密钥错误
```
❌ 无法加载 MMKV 文件: 解密失败
```
**解决**: 检查提供的加密密钥是否正确

### 4. 权限问题
```
❌ 创建解压目录失败: 权限被拒绝
```
**解决**: 确保对目标目录有写权限

## 测试命令

使用提供的测试目录：

```bash
# 全量导出测试（无搜索关键字）
mmkvreader "E:\360MoveData\Users\LiShixi\Desktop\CK20240714"

# 基本测试（默认按key搜索，智能线程数）
mmkvreader "E:\360MoveData\Users\LiShixi\Desktop\CK20240714" --search "AZEROTH"

# 多关键字模糊搜索测试
mmkvreader "E:\360MoveData\Users\LiShixi\Desktop\CK20240714" --search "TOKEN,ADVERTISER_ID,GIFSHOW" --mode value

# 完整关键字测试（直接在解压根目录搜索）
mmkvreader "E:\360MoveData\Users\LiShixi\Desktop\CK20240714" \
  --search "token,advertiser_id,gifshow_,pro_auth_info,devicelD,userlD,passToken,api_st,kwtk,summonArrowConfig,enableShowRocketP" \
  --mode value --zip no

# 调试模式测试
mmkvreader "E:\360MoveData\Users\LiShixi\Desktop\CK20240714" --search "test" --debug

# 单文件测试
mmkvreader "E:\360MoveData\Users\LiShixi\Desktop\CK20240714\example.tgz" --search "config"

# 子命令测试
mmkvreader extract "E:\360MoveData\Users\LiShixi\Desktop\CK20240714"
mmkvreader extract --force "E:\360MoveData\Users\LiShixi\Desktop\CK20240714"
mmkvreader zip "E:\360MoveData\Users\LiShixi\Desktop\CK20240714"
mmkvreader clean "E:\360MoveData\Users\LiShixi\Desktop\CK20240714"
mmkvreader test

# 自定义线程数测试
mmkvreader "E:\360MoveData\Users\LiShixi\Desktop\CK20240714" --search "test" --threads 4 --cleanup yes
```

## 版本信息

查看版本：
```bash
mmkvreader --version
```

查看帮助：
```bash
mmkvreader --help
```
