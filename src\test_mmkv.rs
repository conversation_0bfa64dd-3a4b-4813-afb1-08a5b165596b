use crate::Mmkv;

pub fn test_mmkv_with_params() {
    println!("🧪 开始MMKV测试");
    println!("{}", "=".repeat(50));
    
    let crypt_key = "fd9f5bef68c54a1ecf70757a6d6f565b";
    let mmkv_id = "azeroth";
    let root_path = r"E:\360MoveData\Users\LiShixi\Desktop\CK20240714";
    
    println!("📋 测试参数:");
    println!("   密钥: {}", crypt_key);
    println!("   mmapID: {}", mmkv_id);
    println!("   rootPath: {}", root_path);
    println!();
    
    // 检查根路径是否存在
    let root_path_obj = std::path::Path::new(root_path);
    if !root_path_obj.exists() {
        println!("❌ 错误: 根路径不存在: {}", root_path);
        return;
    }
    
    println!("✅ 根路径存在: {}", root_path);
    
    // 查找所有包含azeroth文件的子目录
    println!("🔍 在根目录下查找azeroth文件...");
    let mut found_azeroth_files = Vec::new();
    
    if let Ok(entries) = std::fs::read_dir(root_path_obj) {
        for entry in entries {
            if let Ok(entry) = entry {
                let path = entry.path();
                if path.is_dir() {
                    let azeroth_file = path.join("azeroth");
                    if azeroth_file.exists() {
                        found_azeroth_files.push((path.clone(), azeroth_file));
                        println!("📁 找到azeroth文件: {}", path.join("azeroth").display());
                        
                        // 获取文件大小
                        if let Ok(metadata) = std::fs::metadata(&path.join("azeroth")) {
                            println!("   📊 文件大小: {} 字节", metadata.len());
                        }
                    }
                }
            }
        }
    }
    
    if found_azeroth_files.is_empty() {
        println!("❌ 错误: 在根目录下没有找到任何azeroth文件");
        return;
    }
    
    println!("\n📊 总共找到 {} 个azeroth文件", found_azeroth_files.len());
    
    // 对每个找到的azeroth文件进行测试
    for (i, (dir_path, azeroth_file)) in found_azeroth_files.iter().enumerate() {
        println!("\n{}", "=".repeat(60));
        println!("🧪 测试第 {} 个azeroth文件", i + 1);
        println!("📁 目录: {}", dir_path.display());
        println!("📄 文件: {}", azeroth_file.display());
        
        println!("\n🔧 初始化MMKV库...");

        // 初始化MMKV库，使用Error级别日志减少输出
        match Mmkv::initialize_with_log_level(dir_path, 3) { // 3 = MMKVLogError
            Ok(_) => {
                println!("✅ MMKV库初始化成功 (日志级别: Error)");
            }
            Err(code) => {
                println!("⚠️  MMKV库初始化失败，错误代码: {}", code);
                println!("   继续尝试加载...");
            }
        }
        
        println!("🔄 尝试加载MMKV...");
        
        // 尝试加载MMKV
        match Mmkv::with_id(
            mmkv_id,
            0,
            crate::MMKV_SINGLE_PROCESS,
            Some(crypt_key),
            Some(dir_path.to_str().unwrap()),
            0,
        ) {
            Some(mmkv) => {
                println!("✅ MMKV加载成功!");
                
                // 获取所有keys
                println!("\n🔑 获取所有keys...");
                let all_keys = mmkv.all_keys();
                println!("📊 总key数量: {}", all_keys.len());
                
                if all_keys.is_empty() {
                    println!("⚠️  没有找到任何key");
                } else {
                    println!("\n📋 所有keys列表:");
                    for (j, key) in all_keys.iter().enumerate() {
                        println!("  {}: {}", j + 1, key);
                    }
                    
                    println!("\n🔍 遍历所有key的值:");
                    println!("{}", "-".repeat(50));
                    
                    for key in &all_keys {
                        let value = mmkv.get_value(key);
                        match value {
                            crate::MmkvValue::String(s) => {
                                println!("🔤 [{}] = \"{}\"", key, &s[..std::cmp::min(200, s.len())]);
                                if s.len() > 200 {
                                    println!("   ... (内容被截断，总长度: {} 字符)", s.len());
                                }
                            }
                            crate::MmkvValue::Integer(i) => {
                                println!("🔢 [{}] = {}", key, i);
                            }
                            crate::MmkvValue::Corrupted(err) => {
                                println!("💥 [{}] = <corrupted: {}>", key, err);
                            }
                            crate::MmkvValue::Unknown => {
                                println!("❓ [{}] = <unknown type>", key);
                            }
                        }
                    }
                }
                
                // 成功加载，跳出循环
                break;
            }
            None => {
                println!("❌ MMKV加载失败!");
                
                // 尝试不使用加密
                println!("🔓 尝试不使用加密...");
                match Mmkv::with_id(
                    mmkv_id,
                    0,
                    crate::MMKV_SINGLE_PROCESS,
                    None,
                    Some(dir_path.to_str().unwrap()),
                    0,
                ) {
                    Some(mmkv) => {
                        println!("✅ 无加密模式加载成功!");
                        let all_keys = mmkv.all_keys();
                        println!("📊 总key数量: {}", all_keys.len());
                        
                        if !all_keys.is_empty() {
                            println!("📋 前10个keys:");
                            for (j, key) in all_keys.iter().take(10).enumerate() {
                                println!("  {}: {}", j + 1, key);
                            }
                        }
                        break;
                    }
                    None => {
                        println!("❌ 无加密模式也加载失败");
                    }
                }
            }
        }
        
        println!("🏁 第 {} 个文件测试完成", i + 1);
    }
    
    println!("\n🏁 所有测试完成");
}
