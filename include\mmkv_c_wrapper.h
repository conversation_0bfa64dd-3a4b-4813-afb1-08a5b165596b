#ifndef MMKV_C_WRAPPER_H
#define MMKV_C_WRAPPER_H

#ifdef __cplusplus
extern "C"
{
#endif

    // Forward declaration for the MMKV handle
    typedef void *MMKV_Handle;

    // Initialize MMKV with a root directory
    // Returns 0 on success, non-zero on failure
    int MMKV_initializeMMKV(const char *rootDir);

    // Initialize MMKV with a root directory and log level
    // logLevel: 0=Debug, 1=Info, 2=Warning, 3=Error, 4=None
    // Returns 0 on success, non-zero on failure
    int MMKV_initializeMMKVWithLogLevel(const char *rootDir, int logLevel);

    // Get the default MMKV instance
    // Returns a handle to the default MMKV instance, or NULL on failure
    MMKV_Handle MMKV_defaultMMKV(void);

    // Get MMKV instance with specific ID and parameters
    // mmapID: unique identifier for the MMKV instance
    // size: initial size (use 0 for default)
    // mode: MMKV mode (1 = single process, 2 = multi process)
    // cryptKey: encryption key (can be NULL for no encryption)
    // rootPath: custom root path (can be NULL to use default)
    // expectedCapacity: expected capacity (use 0 for default)
    // Returns a handle to the MMKV instance, or NULL on failure
    MMKV_Handle MMKV_mmkvWithID(const char *mmapID, int size, int mode, const char *cryptKey, const char *rootPath, int expectedCapacity);

    // Set an int32 value for a key
    // Returns 1 on success, 0 on failure
    int MMKV_setInt32(MMKV_Handle handle, const char *key, int value);

    // Get an int32 value for a key
    // Returns the value if found, or defaultValue if not found
    int MMKV_getInt32(MMKV_Handle handle, const char *key, int defaultValue);

    // Set a string value for a key
    // Returns 1 on success, 0 on failure
    int MMKV_setString(MMKV_Handle handle, const char *key, const char *value);

    // Get a string value for a key
    // Returns a newly allocated string that must be freed with MMKV_freeString
    // Returns NULL if key not found
    char *MMKV_getString(MMKV_Handle handle, const char *key);

    // Free a string returned by MMKV_getString
    void MMKV_freeString(char *str);

    // Check if a key exists
    // Returns 1 if key exists, 0 if not
    int MMKV_containsKey(MMKV_Handle handle, const char *key);

    // Remove a key
    // Returns 1 on success, 0 on failure
    int MMKV_removeValueForKey(MMKV_Handle handle, const char *key);

    // Get all keys
    // Returns an array of strings terminated by NULL
    // Each string and the array itself must be freed with MMKV_freeStringArray
    char **MMKV_allKeys(MMKV_Handle handle);

    // Free a string array returned by MMKV_allKeys
    void MMKV_freeStringArray(char **array);

#ifdef __cplusplus
}
#endif

#endif // MMKV_C_WRAPPER_H
