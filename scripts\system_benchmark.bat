@echo off
REM MMKV Reader System Performance Analysis
REM This script analyzes system performance characteristics

setlocal enabledelayedexpansion

if "%~1"=="" (
    echo Usage: %0 ^<mmkv_binary_path^> [test_data_dir]
    echo Example: %0 target\release\mmkvreader.exe [E:\test_data]
    echo.
    echo If test_data_dir is not provided, only system analysis will be performed
    exit /b 1
)

set "BINARY_PATH=%~1"
set "TEST_DATA_DIR=%~2"

echo ========================================
echo MMKV Reader System Performance Analysis
echo ========================================
echo Binary: %BINARY_PATH%
if not "%TEST_DATA_DIR%"=="" (
    echo Test Data: %TEST_DATA_DIR%
) else (
    echo Test Data: Not provided - system analysis only
)
echo ========================================
echo.

REM Check if binary exists
if not exist "%BINARY_PATH%" (
    echo Error: Binary file not found: %BINARY_PATH%
    pause
    exit /b 1
)

REM System Information
echo Collecting system information...
echo.

REM CPU Information
echo CPU Information:
wmic cpu get Name,NumberOfCores,NumberOfLogicalProcessors,MaxClockSpeed /format:table
echo.

REM Memory Information
echo Memory Information:
wmic computersystem get TotalPhysicalMemory /format:list | find "TotalPhysicalMemory"
wmic OS get FreePhysicalMemory,TotalVisibleMemorySize /format:table
echo.

REM Disk Information
echo Disk Information:
wmic logicaldisk get Size,FreeSpace,FileSystem,DeviceID /format:table
echo.

REM Check if test data directory exists
if not "%TEST_DATA_DIR%"=="" (
    if exist "%TEST_DATA_DIR%" (
        echo Test Data Directory Analysis:
        echo Directory: %TEST_DATA_DIR%
        
        REM Count tgz files
        set TGZ_COUNT=0
        for %%f in ("%TEST_DATA_DIR%\*.tgz") do (
            set /a TGZ_COUNT+=1
        )
        echo TGZ files found: !TGZ_COUNT!
        
        if !TGZ_COUNT! gtr 0 (
            echo.
            echo Running performance tests...
            echo.
            
            REM Quick test with different thread counts
            for %%t in (1 2 4 8) do (
                echo Testing %%t threads...
                echo Start: !time!
                "%BINARY_PATH%" "%TEST_DATA_DIR%" --search "test" --threads %%t --cleanup yes --zip no > nul 2>&1
                echo End: !time!
                echo.
            )
        ) else (
            echo No TGZ files found for testing.
        )
    ) else (
        echo Test data directory not found: %TEST_DATA_DIR%
    )
) else (
    echo No test data directory provided.
)

echo.
echo ========================================
echo Performance Recommendations
echo ========================================
echo.

REM Get CPU cores for recommendations
for /f "skip=1" %%i in ('wmic cpu get NumberOfLogicalProcessors') do (
    if not "%%i"=="" (
        set "LOGICAL_CORES=%%i"
        goto :got_logical_cores
    )
)
:got_logical_cores

for /f "skip=1" %%i in ('wmic cpu get NumberOfCores') do (
    if not "%%i"=="" (
        set "PHYSICAL_CORES=%%i"
        goto :got_physical_cores
    )
)
:got_physical_cores

echo CPU Analysis:
echo - Physical cores: %PHYSICAL_CORES%
echo - Logical cores: %LOGICAL_CORES%

if %LOGICAL_CORES% gtr %PHYSICAL_CORES% (
    echo - Hyperthreading: Enabled
    echo - Recommended threads: %PHYSICAL_CORES% to !LOGICAL_CORES!
) else (
    echo - Hyperthreading: Disabled
    echo - Recommended threads: 1 to %PHYSICAL_CORES%
)

echo.
echo General Recommendations:
echo 1. For I/O intensive tasks: Use thread count = Physical cores
echo 2. For CPU intensive tasks: Use thread count = Logical cores
echo 3. For mixed workloads: Start with Physical cores - 1
echo 4. Always test with your specific data to find optimal settings
echo.

echo.
echo Memory Recommendations:
for /f "tokens=2 delims==" %%i in ('wmic computersystem get TotalPhysicalMemory /value ^| find "="') do (
    if not "%%i"=="" (
        REM Convert bytes to GB (approximate)
        set TOTAL_BYTES=%%i
        set /a TOTAL_GB=!TOTAL_BYTES:~0,-9!
        if !TOTAL_GB! lss 1 set TOTAL_GB=1
    )
)
echo - Total RAM: Approximately %TOTAL_GB%GB

if %TOTAL_GB% geq 16 (
    echo - Memory: Excellent for large datasets
) else if %TOTAL_GB% geq 8 (
    echo - Memory: Good for moderate datasets
    echo - Consider enabling cleanup mode for large datasets
) else (
    echo - Memory: Limited - Use cleanup mode and lower thread counts
)

echo.
echo ========================================
echo System Performance Test Commands
echo ========================================
echo.
echo To test your system performance, try these commands:
echo.
echo 1. Basic test:
echo    %BINARY_PATH% [your_data_dir] --search "test"
echo.
echo 2. Performance analysis:
echo    %BINARY_PATH% [your_data_dir] --search "test" --perf
echo.
echo 3. Built-in benchmark:
echo    %BINARY_PATH% benchmark [your_data_dir]
echo.
echo 4. Thread optimization test:
echo    %BINARY_PATH% [your_data_dir] --search "test" --threads 1
echo    %BINARY_PATH% [your_data_dir] --search "test" --threads %PHYSICAL_CORES%
echo    %BINARY_PATH% [your_data_dir] --search "test" --threads %LOGICAL_CORES%
echo.

REM Generate system report
echo Generating system report...
echo MMKV Reader System Analysis Report > system_report.txt
echo ======================================== >> system_report.txt
echo Analysis time: %date% %time% >> system_report.txt
echo Binary: %BINARY_PATH% >> system_report.txt
if not "%TEST_DATA_DIR%"=="" echo Test data: %TEST_DATA_DIR% >> system_report.txt
echo Physical cores: %PHYSICAL_CORES% >> system_report.txt
echo Logical cores: %LOGICAL_CORES% >> system_report.txt
echo Total RAM: %TOTAL_GB%GB >> system_report.txt
echo ======================================== >> system_report.txt
echo. >> system_report.txt
echo For detailed system information, see console output above >> system_report.txt

echo System report saved to: system_report.txt
echo.

pause
