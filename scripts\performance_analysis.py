#!/usr/bin/env python3
"""
MMKV Reader 性能分析脚本
用于自动化性能测试和瓶颈检测
"""

import subprocess
import time
import json
import sys
import os
import argparse
from pathlib import Path
import matplotlib.pyplot as plt
import pandas as pd

class PerformanceAnalyzer:
    def __init__(self, mmkv_binary_path, test_data_dir):
        self.mmkv_binary = Path(mmkv_binary_path)
        self.test_data_dir = Path(test_data_dir)
        self.results = []
        
        if not self.mmkv_binary.exists():
            raise FileNotFoundError(f"MMKV binary not found: {mmkv_binary_path}")
        if not self.test_data_dir.exists():
            raise FileNotFoundError(f"Test data directory not found: {test_data_dir}")

    def run_performance_test(self, threads, search_terms="config,user,token", rounds=3):
        """运行性能测试"""
        print(f"🧪 测试线程数: {threads}")
        
        results = []
        for round_num in range(1, rounds + 1):
            print(f"  轮次 {round_num}/{rounds}")
            
            start_time = time.time()
            
            cmd = [
                str(self.mmkv_binary),
                str(self.test_data_dir),
                "--search", search_terms,
                "--threads", str(threads),
                "--cleanup", "yes",
                "--zip", "no"
            ]
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
                elapsed_time = time.time() - start_time
                
                if result.returncode == 0:
                    # 解析输出获取统计信息
                    output_lines = result.stdout.split('\n')
                    processed_files = 0
                    total_matches = 0
                    
                    for line in output_lines:
                        if "处理" in line and "文件" in line:
                            # 解析类似 "✅ 多关键字搜索完成: 处理 100 文件，找到 50 匹配"
                            parts = line.split()
                            for i, part in enumerate(parts):
                                if part == "处理" and i + 1 < len(parts):
                                    try:
                                        processed_files = int(parts[i + 1])
                                    except ValueError:
                                        pass
                                elif part == "找到" and i + 1 < len(parts):
                                    try:
                                        total_matches = int(parts[i + 1])
                                    except ValueError:
                                        pass
                    
                    results.append({
                        'round': round_num,
                        'threads': threads,
                        'elapsed_time': elapsed_time,
                        'processed_files': processed_files,
                        'total_matches': total_matches,
                        'files_per_second': processed_files / elapsed_time if elapsed_time > 0 else 0,
                        'success': True
                    })
                    
                    print(f"    耗时: {elapsed_time:.2f}s | 文件: {processed_files} | 匹配: {total_matches} | 速度: {processed_files/elapsed_time:.1f} 文件/秒")
                else:
                    print(f"    ❌ 测试失败: {result.stderr}")
                    results.append({
                        'round': round_num,
                        'threads': threads,
                        'elapsed_time': elapsed_time,
                        'processed_files': 0,
                        'total_matches': 0,
                        'files_per_second': 0,
                        'success': False,
                        'error': result.stderr
                    })
                    
            except subprocess.TimeoutExpired:
                print(f"    ⏰ 测试超时")
                results.append({
                    'round': round_num,
                    'threads': threads,
                    'elapsed_time': 300,
                    'processed_files': 0,
                    'total_matches': 0,
                    'files_per_second': 0,
                    'success': False,
                    'error': 'Timeout'
                })
        
        return results

    def run_comprehensive_analysis(self):
        """运行全面的性能分析"""
        print("🚀 开始全面性能分析")
        print("=" * 60)
        
        # 测试不同线程数
        thread_counts = [1, 2, 4, 8, 16, 32, 64]
        
        for threads in thread_counts:
            test_results = self.run_performance_test(threads)
            self.results.extend(test_results)
            print()
        
        # 分析结果
        self.analyze_results()
        
        # 生成报告
        self.generate_report()
        
        # 生成图表
        self.generate_charts()

    def analyze_results(self):
        """分析测试结果"""
        print("📊 分析测试结果")
        print("-" * 40)
        
        # 按线程数分组计算平均值
        df = pd.DataFrame(self.results)
        successful_results = df[df['success'] == True]
        
        if successful_results.empty:
            print("❌ 没有成功的测试结果")
            return
        
        grouped = successful_results.groupby('threads').agg({
            'elapsed_time': 'mean',
            'processed_files': 'mean',
            'total_matches': 'mean',
            'files_per_second': 'mean'
        }).round(2)
        
        print("平均性能指标:")
        print(grouped)
        
        # 找出最佳线程数
        best_threads = grouped['files_per_second'].idxmax()
        best_speed = grouped.loc[best_threads, 'files_per_second']
        
        print(f"\n🏆 最佳线程数: {best_threads} (速度: {best_speed:.1f} 文件/秒)")
        
        # 性能瓶颈分析
        print("\n🔍 性能瓶颈分析:")
        
        # 检查线程扩展性
        single_thread_speed = grouped.loc[1, 'files_per_second'] if 1 in grouped.index else 0
        if single_thread_speed > 0:
            for threads in grouped.index:
                if threads > 1:
                    speedup = grouped.loc[threads, 'files_per_second'] / single_thread_speed
                    efficiency = speedup / threads * 100
                    print(f"  {threads} 线程: 加速比 {speedup:.1f}x, 效率 {efficiency:.1f}%")
                    
                    if efficiency < 50:
                        print(f"    ⚠️  效率较低，可能存在线程竞争或I/O瓶颈")
                    elif speedup < threads * 0.7:
                        print(f"    ⚠️  扩展性不佳，可能受限于I/O或内存带宽")

    def generate_report(self):
        """生成性能报告"""
        report_file = "performance_report.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"📄 性能报告已保存到: {report_file}")

    def generate_charts(self):
        """生成性能图表"""
        try:
            df = pd.DataFrame(self.results)
            successful_results = df[df['success'] == True]
            
            if successful_results.empty:
                print("❌ 没有数据可用于生成图表")
                return
            
            # 按线程数分组计算平均值
            grouped = successful_results.groupby('threads').agg({
                'elapsed_time': 'mean',
                'files_per_second': 'mean'
            })
            
            # 创建图表
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
            
            # 处理速度图表
            ax1.plot(grouped.index, grouped['files_per_second'], 'bo-')
            ax1.set_xlabel('线程数')
            ax1.set_ylabel('处理速度 (文件/秒)')
            ax1.set_title('线程数 vs 处理速度')
            ax1.grid(True)
            
            # 处理时间图表
            ax2.plot(grouped.index, grouped['elapsed_time'], 'ro-')
            ax2.set_xlabel('线程数')
            ax2.set_ylabel('处理时间 (秒)')
            ax2.set_title('线程数 vs 处理时间')
            ax2.grid(True)
            
            plt.tight_layout()
            plt.savefig('performance_analysis.png', dpi=300, bbox_inches='tight')
            print("📈 性能图表已保存到: performance_analysis.png")
            
        except ImportError:
            print("⚠️  matplotlib 未安装，跳过图表生成")
        except Exception as e:
            print(f"❌ 生成图表失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='MMKV Reader 性能分析工具')
    parser.add_argument('binary', help='MMKV Reader 可执行文件路径')
    parser.add_argument('data_dir', help='测试数据目录')
    parser.add_argument('--search', default='config,user,token', help='搜索关键字')
    parser.add_argument('--rounds', type=int, default=3, help='每个配置的测试轮数')
    
    args = parser.parse_args()
    
    try:
        analyzer = PerformanceAnalyzer(args.binary, args.data_dir)
        analyzer.run_comprehensive_analysis()
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
