{"files.associations": {"iterator": "cpp", "iosfwd": "cpp", "list": "cpp", "span": "cpp", "vector": "cpp", "xstring": "cpp", "xutility": "cpp", "cmath": "cpp", "cctype": "cpp", "compare": "cpp", "concepts": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "cwchar": "cpp", "exception": "cpp", "initializer_list": "cpp", "limits": "cpp", "new": "cpp", "string": "cpp", "tuple": "cpp", "type_traits": "cpp", "unordered_map": "cpp", "utility": "cpp", "xhash": "cpp", "xmemory": "cpp", "xtr1common": "cpp"}}