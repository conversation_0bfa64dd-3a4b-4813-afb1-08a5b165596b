@echo off
REM MMKV Reader 性能分析脚本 (Windows)
REM 用法: performance_analysis.bat <mmkv_binary_path> <test_data_dir>

setlocal enabledelayedexpansion

if "%~2"=="" (
    echo 用法: %0 ^<mmkv_binary_path^> ^<test_data_dir^>
    echo 示例: %0 target\release\mmkvreader.exe E:\test_data
    exit /b 1
)

set BINARY_PATH=%~1
set TEST_DATA_DIR=%~2
set SEARCH_TERMS=config,user,token
set ROUNDS=3

echo 🚀 MMKV Reader 性能分析
echo ========================================
echo 📁 可执行文件: %BINARY_PATH%
echo 📁 测试数据: %TEST_DATA_DIR%
echo 🔍 搜索关键字: %SEARCH_TERMS%
echo 🔄 测试轮数: %ROUNDS%
echo.

REM 检查文件是否存在
if not exist "%BINARY_PATH%" (
    echo ❌ 错误: 可执行文件不存在: %BINARY_PATH%
    exit /b 1
)

if not exist "%TEST_DATA_DIR%" (
    echo ❌ 错误: 测试数据目录不存在: %TEST_DATA_DIR%
    exit /b 1
)

REM 获取CPU核心数
for /f "tokens=2 delims==" %%i in ('wmic cpu get NumberOfCores /value ^| find "="') do set CPU_CORES=%%i

echo 🧵 CPU核心数: %CPU_CORES%
echo.

REM 测试不同线程数配置
set THREAD_CONFIGS=1 2 4 8 16 32

for %%t in (%THREAD_CONFIGS%) do (
    echo 🧪 测试线程数: %%t
    echo ----------------------------------------
    
    set /a TOTAL_TIME=0
    set /a TOTAL_FILES=0
    set /a TOTAL_MATCHES=0
    
    for /l %%r in (1,1,%ROUNDS%) do (
        echo   轮次 %%r/%ROUNDS%
        
        REM 记录开始时间
        for /f "tokens=1-4 delims=:.," %%a in ("%time%") do (
            set /a START_TIME=((%%a*60+1%%b %% 100)*60+1%%c %% 100)*100+1%%d %% 100
        )
        
        REM 运行测试
        "%BINARY_PATH%" "%TEST_DATA_DIR%" --search "%SEARCH_TERMS%" --threads %%t --cleanup yes --zip no > temp_output.txt 2>&1
        
        REM 记录结束时间
        for /f "tokens=1-4 delims=:.," %%a in ("%time%") do (
            set /a END_TIME=((%%a*60+1%%b %% 100)*60+1%%c %% 100)*100+1%%d %% 100
        )
        
        REM 计算耗时（毫秒）
        set /a ELAPSED_MS=!END_TIME!-!START_TIME!
        if !ELAPSED_MS! lss 0 set /a ELAPSED_MS+=24*60*60*100
        
        REM 解析输出结果
        for /f "tokens=*" %%i in (temp_output.txt) do (
            set LINE=%%i
            echo !LINE! | findstr /c:"处理" | findstr /c:"文件" >nul
            if !errorlevel! equ 0 (
                REM 解析处理的文件数和匹配数
                for /f "tokens=4,8" %%a in ("!LINE!") do (
                    set PROCESSED_FILES=%%a
                    set MATCHES=%%b
                )
            )
        )
        
        REM 计算速度
        if defined PROCESSED_FILES if !ELAPSED_MS! gtr 0 (
            set /a SPEED=!PROCESSED_FILES!*1000/!ELAPSED_MS!
            echo     耗时: !ELAPSED_MS!ms ^| 文件: !PROCESSED_FILES! ^| 匹配: !MATCHES! ^| 速度: !SPEED! 文件/秒
        ) else (
            echo     ❌ 测试失败或无法解析结果
        )
    )
    
    echo.
)

REM 清理临时文件
if exist temp_output.txt del temp_output.txt

echo ✅ 性能分析完成!
echo.
echo 💡 性能优化建议:
echo   - 使用SSD存储可显著提升I/O性能
echo   - 线程数通常设置为CPU核心数-1最优
echo   - 大量小文件时，适中的线程数比高线程数更有效
echo   - 启用 --cleanup 可节省磁盘空间但略微影响性能
echo.

REM 生成简单的性能报告
echo 📄 生成性能报告...
echo MMKV Reader 性能测试报告 > performance_report.txt
echo 测试时间: %date% %time% >> performance_report.txt
echo 测试数据: %TEST_DATA_DIR% >> performance_report.txt
echo CPU核心数: %CPU_CORES% >> performance_report.txt
echo 搜索关键字: %SEARCH_TERMS% >> performance_report.txt
echo. >> performance_report.txt
echo 详细结果请查看控制台输出 >> performance_report.txt

echo 📄 性能报告已保存到: performance_report.txt

pause
