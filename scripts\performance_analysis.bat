@echo off
REM MMKV Reader 性能分析脚本 (Windows)
REM 用法: performance_analysis.bat <mmkv_binary_path> <test_data_dir>

setlocal enabledelayedexpansion

if "%~2"=="" (
    echo 用法: %0 ^<mmkv_binary_path^> ^<test_data_dir^>
    echo 示例: %0 target\release\mmkvreader.exe E:\test_data
    exit /b 1
)

set "BINARY_PATH=%~1"
set "TEST_DATA_DIR=%~2"
set "SEARCH_TERMS=config,user,token"
set "ROUNDS=3"

echo MMKV Reader 性能分析
echo ========================================
echo 可执行文件: %BINARY_PATH%
echo 测试数据: %TEST_DATA_DIR%
echo 搜索关键字: %SEARCH_TERMS%
echo 测试轮数: %ROUNDS%
echo.

REM 检查文件是否存在
if not exist "%BINARY_PATH%" (
    echo 错误: 可执行文件不存在: %BINARY_PATH%
    exit /b 1
)

if not exist "%TEST_DATA_DIR%" (
    echo 错误: 测试数据目录不存在: %TEST_DATA_DIR%
    exit /b 1
)

REM 获取CPU核心数
for /f "skip=1 tokens=*" %%i in ('wmic cpu get NumberOfCores /value') do (
    for /f "tokens=2 delims==" %%j in ("%%i") do (
        if not "%%j"=="" set "CPU_CORES=%%j"
    )
)

echo CPU核心数: %CPU_CORES%
echo.

REM 测试不同线程数配置
set "THREAD_CONFIGS=1 2 4 8 16"

for %%t in (%THREAD_CONFIGS%) do (
    echo 测试线程数: %%t
    echo ----------------------------------------

    for /l %%r in (1,1,%ROUNDS%) do (
        echo   轮次 %%r/%ROUNDS%

        REM 运行测试并记录时间
        echo 开始时间: !time!

        "%BINARY_PATH%" "%TEST_DATA_DIR%" --search "%SEARCH_TERMS%" --threads %%t --cleanup yes --zip no > temp_output_%%t_%%r.txt 2>&1
        set "TEST_RESULT=!errorlevel!"

        echo 结束时间: !time!

        REM 检查测试是否成功
        if !TEST_RESULT! equ 0 (
            echo     测试成功

            REM 显示输出文件的最后几行（包含统计信息）
            for /f "tokens=*" %%i in (temp_output_%%t_%%r.txt) do (
                set "LAST_LINE=%%i"
            )
            echo     结果: !LAST_LINE!
        ) else (
            echo     测试失败，错误代码: !TEST_RESULT!
            echo     错误信息:
            type temp_output_%%t_%%r.txt
        )

        echo.
    )

    echo.
)

REM 清理临时文件
echo 清理临时文件...
for %%f in (temp_output_*.txt) do (
    if exist "%%f" del "%%f"
)

echo 性能分析完成!
echo.
echo 性能优化建议:
echo   - 使用SSD存储可显著提升I/O性能
echo   - 线程数通常设置为CPU核心数-1最优
echo   - 大量小文件时，适中的线程数比高线程数更有效
echo   - 启用 --cleanup 可节省磁盘空间但略微影响性能
echo.

REM 生成简单的性能报告
echo 生成性能报告...
echo MMKV Reader 性能测试报告 > performance_report.txt
echo 测试时间: %date% %time% >> performance_report.txt
echo 测试数据: %TEST_DATA_DIR% >> performance_report.txt
echo CPU核心数: %CPU_CORES% >> performance_report.txt
echo 搜索关键字: %SEARCH_TERMS% >> performance_report.txt
echo. >> performance_report.txt
echo 详细结果请查看控制台输出 >> performance_report.txt

echo 性能报告已保存到: performance_report.txt

pause
