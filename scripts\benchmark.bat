@echo off
REM MMKV Reader Performance Benchmark Script
REM Usage: benchmark.bat <mmkv_binary_path> <test_data_dir>

setlocal enabledelayedexpansion

if "%~2"=="" (
    echo Usage: %0 ^<mmkv_binary_path^> ^<test_data_dir^>
    echo Example: %0 target\release\mmkvreader.exe E:\test_data
    exit /b 1
)

set "BINARY_PATH=%~1"
set "TEST_DATA_DIR=%~2"
set "SEARCH_TERMS=config,user,token"

echo ========================================
echo MMKV Reader Performance Benchmark
echo ========================================
echo Binary: %BINARY_PATH%
echo Test Data: %TEST_DATA_DIR%
echo Search Terms: %SEARCH_TERMS%
echo ========================================
echo.

REM Check if files exist
if not exist "%BINARY_PATH%" (
    echo Error: Binary file not found: %BINARY_PATH%
    pause
    exit /b 1
)

if not exist "%TEST_DATA_DIR%" (
    echo Error: Test data directory not found: %TEST_DATA_DIR%
    pause
    exit /b 1
)

REM Get CPU core count
for /f "skip=1" %%i in ('wmic cpu get NumberOfCores') do (
    if not "%%i"=="" (
        set "CPU_CORES=%%i"
        goto :got_cores
    )
)
:got_cores

echo CPU Cores: %CPU_CORES%
echo.

REM Start performance tests
echo Starting performance tests...
echo.

REM Test 1: Single thread
echo [1/5] Testing single thread performance...
echo Start time: %time%
"%BINARY_PATH%" "%TEST_DATA_DIR%" --search "%SEARCH_TERMS%" --threads 1 --cleanup yes --zip no
echo End time: %time%
echo.

REM Test 2: 4 threads
echo [2/5] Testing 4 threads performance...
echo Start time: %time%
"%BINARY_PATH%" "%TEST_DATA_DIR%" --search "%SEARCH_TERMS%" --threads 4 --cleanup yes --zip no
echo End time: %time%
echo.

REM Test 3: 8 threads
echo [3/5] Testing 8 threads performance...
echo Start time: %time%
"%BINARY_PATH%" "%TEST_DATA_DIR%" --search "%SEARCH_TERMS%" --threads 8 --cleanup yes --zip no
echo End time: %time%
echo.

REM Test 4: CPU cores threads
echo [4/5] Testing %CPU_CORES% threads performance...
echo Start time: %time%
"%BINARY_PATH%" "%TEST_DATA_DIR%" --search "%SEARCH_TERMS%" --threads %CPU_CORES% --cleanup yes --zip no
echo End time: %time%
echo.

REM Test 5: Built-in benchmark
echo [5/5] Running built-in benchmark...
echo Start time: %time%
"%BINARY_PATH%" benchmark "%TEST_DATA_DIR%" --rounds 2
echo End time: %time%
echo.

echo ========================================
echo Performance tests completed!
echo ========================================
echo.
echo Performance optimization suggestions:
echo 1. Observe the test results above and choose the best thread count
echo 2. Use SSD storage for significant I/O performance improvement
echo 3. Ensure sufficient memory to avoid swap files
echo 4. For large numbers of files, moderate thread counts are usually more effective than very high counts
echo.

REM Generate report
echo Generating test report...
echo MMKV Reader Performance Test Report > benchmark_report.txt
echo ======================================== >> benchmark_report.txt
echo Test time: %date% %time% >> benchmark_report.txt
echo Binary: %BINARY_PATH% >> benchmark_report.txt
echo Test data: %TEST_DATA_DIR% >> benchmark_report.txt
echo CPU cores: %CPU_CORES% >> benchmark_report.txt
echo Search terms: %SEARCH_TERMS% >> benchmark_report.txt
echo ======================================== >> benchmark_report.txt
echo. >> benchmark_report.txt
echo For detailed test results, please check the console output >> benchmark_report.txt
echo It is recommended to use the built-in benchmark for more accurate performance data >> benchmark_report.txt

echo Test report saved to: benchmark_report.txt
echo.

pause
