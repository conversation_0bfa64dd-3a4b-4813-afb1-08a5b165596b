// src/performance.rs
// 性能分析和监控工具

use std::time::{Duration, Instant};
use std::sync::{Arc, Mutex};
use std::collections::HashMap;
use serde::{Serialize, Deserialize};

/// 性能计数器
#[derive(Debug, Clone)]
pub struct PerformanceCounter {
    pub name: String,
    pub start_time: Option<Instant>,
    pub total_duration: Duration,
    pub call_count: usize,
    pub min_duration: Option<Duration>,
    pub max_duration: Option<Duration>,
}

impl PerformanceCounter {
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            start_time: None,
            total_duration: Duration::ZERO,
            call_count: 0,
            min_duration: None,
            max_duration: None,
        }
    }

    pub fn start(&mut self) {
        self.start_time = Some(Instant::now());
    }

    pub fn stop(&mut self) {
        if let Some(start) = self.start_time.take() {
            let duration = start.elapsed();
            self.total_duration += duration;
            self.call_count += 1;

            match self.min_duration {
                None => self.min_duration = Some(duration),
                Some(min) if duration < min => self.min_duration = Some(duration),
                _ => {}
            }

            match self.max_duration {
                None => self.max_duration = Some(duration),
                Some(max) if duration > max => self.max_duration = Some(duration),
                _ => {}
            }
        }
    }

    pub fn average_duration(&self) -> Duration {
        if self.call_count > 0 {
            self.total_duration / self.call_count as u32
        } else {
            Duration::ZERO
        }
    }
}

/// 性能监控器
#[derive(Debug)]
pub struct PerformanceMonitor {
    counters: Arc<Mutex<HashMap<String, PerformanceCounter>>>,
    start_time: Instant,
}

impl PerformanceMonitor {
    pub fn new() -> Self {
        Self {
            counters: Arc::new(Mutex::new(HashMap::new())),
            start_time: Instant::now(),
        }
    }

    pub fn start_timer(&self, name: &str) -> PerformanceTimer {
        PerformanceTimer::new(name, self.counters.clone())
    }

    pub fn get_report(&self) -> PerformanceReport {
        let counters = self.counters.lock().unwrap();
        let total_elapsed = self.start_time.elapsed();
        
        let mut counter_reports = Vec::new();
        for (name, counter) in counters.iter() {
            counter_reports.push(CounterReport {
                name: name.clone(),
                total_duration_ms: counter.total_duration.as_millis() as f64,
                average_duration_ms: counter.average_duration().as_millis() as f64,
                min_duration_ms: counter.min_duration.map(|d| d.as_millis() as f64),
                max_duration_ms: counter.max_duration.map(|d| d.as_millis() as f64),
                call_count: counter.call_count,
                percentage_of_total: (counter.total_duration.as_millis() as f64 / total_elapsed.as_millis() as f64) * 100.0,
            });
        }

        // 按总耗时排序
        counter_reports.sort_by(|a, b| b.total_duration_ms.partial_cmp(&a.total_duration_ms).unwrap());

        PerformanceReport {
            total_elapsed_ms: total_elapsed.as_millis() as f64,
            counters: counter_reports,
        }
    }

    pub fn print_report(&self) {
        let report = self.get_report();
        println!("\n🔍 性能分析报告");
        println!("{}", "=".repeat(80));
        println!("总耗时: {:.2}ms ({:.2}s)", report.total_elapsed_ms, report.total_elapsed_ms / 1000.0);
        println!();
        
        println!("{:<25} {:>10} {:>10} {:>10} {:>10} {:>8} {:>8}", 
                 "操作", "总耗时(ms)", "平均(ms)", "最小(ms)", "最大(ms)", "调用次数", "占比(%)");
        println!("{}", "-".repeat(80));
        
        for counter in &report.counters {
            println!("{:<25} {:>10.2} {:>10.2} {:>10.2} {:>10.2} {:>8} {:>7.1}%",
                     counter.name,
                     counter.total_duration_ms,
                     counter.average_duration_ms,
                     counter.min_duration_ms.unwrap_or(0.0),
                     counter.max_duration_ms.unwrap_or(0.0),
                     counter.call_count,
                     counter.percentage_of_total);
        }
        println!("{}", "=".repeat(80));
    }
}

/// 性能计时器（RAII）
pub struct PerformanceTimer {
    name: String,
    start_time: Instant,
    counters: Arc<Mutex<HashMap<String, PerformanceCounter>>>,
}

impl PerformanceTimer {
    fn new(name: &str, counters: Arc<Mutex<HashMap<String, PerformanceCounter>>>) -> Self {
        Self {
            name: name.to_string(),
            start_time: Instant::now(),
            counters,
        }
    }
}

impl Drop for PerformanceTimer {
    fn drop(&mut self) {
        let duration = self.start_time.elapsed();
        let mut counters = self.counters.lock().unwrap();
        
        let counter = counters.entry(self.name.clone())
            .or_insert_with(|| PerformanceCounter::new(&self.name));
        
        counter.total_duration += duration;
        counter.call_count += 1;

        match counter.min_duration {
            None => counter.min_duration = Some(duration),
            Some(min) if duration < min => counter.min_duration = Some(duration),
            _ => {}
        }

        match counter.max_duration {
            None => counter.max_duration = Some(duration),
            Some(max) if duration > max => counter.max_duration = Some(duration),
            _ => {}
        }
    }
}

/// 性能报告
#[derive(Debug, Serialize, Deserialize)]
pub struct PerformanceReport {
    pub total_elapsed_ms: f64,
    pub counters: Vec<CounterReport>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CounterReport {
    pub name: String,
    pub total_duration_ms: f64,
    pub average_duration_ms: f64,
    pub min_duration_ms: Option<f64>,
    pub max_duration_ms: Option<f64>,
    pub call_count: usize,
    pub percentage_of_total: f64,
}

/// 内存使用监控
pub fn get_memory_usage_mb() -> f64 {
    #[cfg(target_os = "windows")]
    {
        use std::mem;
        use std::ptr;
        
        #[repr(C)]
        struct ProcessMemoryCounters {
            cb: u32,
            page_fault_count: u32,
            peak_working_set_size: usize,
            working_set_size: usize,
            quota_peak_paged_pool_usage: usize,
            quota_paged_pool_usage: usize,
            quota_peak_non_paged_pool_usage: usize,
            quota_non_paged_pool_usage: usize,
            pagefile_usage: usize,
            peak_pagefile_usage: usize,
        }

        extern "system" {
            fn GetCurrentProcess() -> *mut std::ffi::c_void;
            fn GetProcessMemoryInfo(
                process: *mut std::ffi::c_void,
                counters: *mut ProcessMemoryCounters,
                cb: u32,
            ) -> i32;
        }

        unsafe {
            let mut counters: ProcessMemoryCounters = mem::zeroed();
            counters.cb = mem::size_of::<ProcessMemoryCounters>() as u32;
            
            let process = GetCurrentProcess();
            if GetProcessMemoryInfo(process, &mut counters, counters.cb) != 0 {
                return counters.working_set_size as f64 / 1024.0 / 1024.0;
            }
        }
    }
    
    #[cfg(target_os = "linux")]
    {
        if let Ok(status) = std::fs::read_to_string("/proc/self/status") {
            for line in status.lines() {
                if line.starts_with("VmRSS:") {
                    if let Some(kb_str) = line.split_whitespace().nth(1) {
                        if let Ok(kb) = kb_str.parse::<f64>() {
                            return kb / 1024.0; // Convert KB to MB
                        }
                    }
                }
            }
        }
    }
    
    0.0 // 无法获取时返回0
}

/// CPU使用率监控（简化版）
pub fn get_cpu_usage_percent() -> f64 {
    // 这是一个简化的实现，实际应用中可能需要更复杂的逻辑
    // 可以使用 sysinfo crate 获得更准确的CPU使用率
    0.0
}
