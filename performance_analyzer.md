# MMKV Reader 性能分析指南

## 概述

本指南介绍如何检测和分析 MMKV Reader 的性能瓶颈，包括内置工具和外部分析方法。

## 1. 内置性能分析

### 启用性能分析模式

```bash
# 启用性能分析
mmkvreader "E:\data" --search "config" --perf

# 同时启用调试和性能分析
mmkvreader "E:\data" --search "config" --debug --perf
```

### 性能报告示例

```
🔍 性能分析报告
================================================================================
总耗时: 45230.50ms (45.23s)

操作                      总耗时(ms)   平均(ms)   最小(ms)   最大(ms)  调用次数   占比(%)
--------------------------------------------------------------------------------
文件解压                    25430.20    127.15      45.30     890.50      200    56.2%
MMKV搜索                     8920.30     44.60      12.10     156.80      200    19.7%
文件I/O                      6780.40     33.90       8.20     125.60      200    15.0%
线程池创建                    2100.60   2100.60    2100.60    2100.60        1     4.6%
JSON序列化                   1999.00      9.99       3.40      45.20      200     4.4%
================================================================================
```

## 2. 外部性能分析工具

### Windows 平台

#### 1. Windows Performance Toolkit (WPT)
```bash
# 安装 WPT
# 从 Windows SDK 安装

# 记录性能数据
wpr -start CPU -start FileIO

# 运行程序
mmkvreader "E:\data" --search "config"

# 停止记录
wpr -stop performance.etl

# 分析数据
wpa performance.etl
```

#### 2. Process Monitor (ProcMon)
- 监控文件系统活动
- 检测I/O瓶颈
- 分析注册表访问

#### 3. Resource Monitor
```bash
# 启动资源监视器
resmon.exe
```

#### 4. Performance Counters
```bash
# 使用 typeperf 监控性能计数器
typeperf "\Process(mmkvreader)\% Processor Time" "\Process(mmkvreader)\Working Set"
```

### Linux 平台

#### 1. perf 工具
```bash
# CPU 性能分析
perf record -g ./target/release/mmkvreader "data" --search "config"
perf report

# 热点函数分析
perf top -p $(pgrep mmkvreader)

# 内存访问分析
perf record -e cache-misses ./target/release/mmkvreader "data" --search "config"
```

#### 2. strace 系统调用跟踪
```bash
# 跟踪系统调用
strace -c ./target/release/mmkvreader "data" --search "config"

# 跟踪文件操作
strace -e trace=file ./target/release/mmkvreader "data" --search "config"
```

#### 3. htop/top 资源监控
```bash
# 实时监控
htop

# 监控特定进程
top -p $(pgrep mmkvreader)
```

#### 4. iotop I/O监控
```bash
# 监控I/O活动
sudo iotop -p $(pgrep mmkvreader)
```

### macOS 平台

#### 1. Instruments
```bash
# 使用 Instruments 进行性能分析
instruments -t "Time Profiler" ./target/release/mmkvreader "data" --search "config"
```

#### 2. Activity Monitor
- 监控CPU、内存、磁盘使用
- 查看进程详细信息

## 3. Rust 专用分析工具

### 1. cargo flamegraph
```bash
# 安装
cargo install flamegraph

# 生成火焰图
cargo flamegraph --bin mmkvreader -- "data" --search "config"
```

### 2. cargo profdata
```bash
# 编译时启用性能分析
RUSTFLAGS="-C instrument-coverage" cargo build --release

# 运行并收集数据
./target/release/mmkvreader "data" --search "config"

# 生成报告
cargo profdata
```

### 3. heaptrack (内存分析)
```bash
# 安装 heaptrack
# Ubuntu: sudo apt install heaptrack
# 其他平台参考官方文档

# 运行内存分析
heaptrack ./target/release/mmkvreader "data" --search "config"

# 分析结果
heaptrack_gui heaptrack.mmkvreader.*.gz
```

## 4. 代码级性能优化

### 1. 编译优化
```toml
# Cargo.toml
[profile.release]
opt-level = 3           # 最高优化级别
lto = true             # 链接时优化
codegen-units = 1      # 减少代码生成单元
panic = "abort"        # 减少二进制大小
```

### 2. 目标特定优化
```bash
# 针对本机CPU优化
RUSTFLAGS="-C target-cpu=native" cargo build --release

# 针对特定CPU特性优化
RUSTFLAGS="-C target-feature=+avx2,+fma" cargo build --release
```

### 3. 内存分配器优化
```toml
# Cargo.toml - 使用更快的内存分配器
[dependencies]
jemallocator = "0.5"

# 或者使用 mimalloc
mimalloc = { version = "0.1", default-features = false }
```

```rust
// main.rs
#[global_allocator]
static ALLOC: jemallocator::Jemalloc = jemallocator::Jemalloc;

// 或者
#[global_allocator]
static ALLOC: mimalloc::MiMalloc = mimalloc::MiMalloc;
```

## 5. 常见性能瓶颈

### 1. I/O 瓶颈
- **症状**: 高磁盘使用率，低CPU使用率
- **解决**: 使用SSD、减少文件操作、批量I/O

### 2. CPU 瓶颈
- **症状**: 高CPU使用率，进程缓慢
- **解决**: 优化算法、并行处理、减少计算复杂度

### 3. 内存瓶颈
- **症状**: 高内存使用、频繁GC、交换文件活动
- **解决**: 减少内存分配、使用对象池、流式处理

### 4. 线程竞争
- **症状**: 多核CPU利用率不均、锁等待
- **解决**: 减少共享状态、使用无锁数据结构

### 5. 网络瓶颈（如适用）
- **症状**: 网络延迟高、带宽利用率低
- **解决**: 连接池、批量请求、压缩

## 6. 性能测试脚本

### 基准测试脚本
```bash
#!/bin/bash
# benchmark.sh

echo "MMKV Reader 性能基准测试"
echo "========================"

# 测试数据
TEST_DIR="test_data"
SEARCH_TERMS="config,user,token"

# 不同线程数测试
for threads in 1 4 8 16 32; do
    echo "测试线程数: $threads"
    time ./target/release/mmkvreader "$TEST_DIR" \
        --search "$SEARCH_TERMS" \
        --threads $threads \
        --perf \
        --cleanup yes
    echo "------------------------"
done
```

### 内存泄漏检测
```bash
# 使用 valgrind (Linux)
valgrind --tool=memcheck --leak-check=full \
    ./target/release/mmkvreader "data" --search "config"

# 使用 AddressSanitizer
RUSTFLAGS="-Z sanitizer=address" cargo run --target x86_64-unknown-linux-gnu \
    -- "data" --search "config"
```

## 7. 持续性能监控

### 1. 自动化基准测试
```yaml
# .github/workflows/benchmark.yml
name: Performance Benchmark
on: [push, pull_request]

jobs:
  benchmark:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Run benchmark
      run: |
        cargo build --release
        ./scripts/benchmark.sh
```

### 2. 性能回归检测
- 在CI/CD中集成性能测试
- 设置性能阈值
- 自动报告性能退化

## 8. 性能优化建议

### 1. 数据结构优化
- 使用合适的数据结构（HashMap vs BTreeMap）
- 预分配容量避免重新分配
- 使用引用减少克隆

### 2. 算法优化
- 选择合适的搜索算法
- 减少不必要的计算
- 缓存计算结果

### 3. 并发优化
- 合理设置线程数
- 避免过度并发
- 使用工作窃取调度

### 4. 内存优化
- 及时释放资源
- 使用流式处理
- 避免内存碎片

通过这些工具和方法，你可以全面分析和优化 MMKV Reader 的性能。
