[package]
name = "mmkvreader"
version = "0.1.0"
edition = "2024"
build = "build.rs"

[dependencies]
flate2 = "1.0"
tar = "0.4"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
rayon = "1.8"
walkdir = "2.4"
regex = "1.10"
tokio = { version = "1.0", features = ["full"] }
anyhow = "1.0"
chrono = { version = "0.4", features = ["serde"] }
clap = { version = "4.4", features = ["derive"] }
zip = "0.6"
indicatif = "0.17"
num_cpus = "1.16"
cpp = "0.5"
libc = "0.2"
cpp_common = "0.5"

ffi = "0.1.1"

[build-dependencies]
bindgen = "0.72.0"
cc = "1.2.27"


[profile.release]
split-debuginfo = "unpacked"
opt-level = "s"
