# MMKV Reader 性能分析完整指南

## 快速开始

### 1. 基本性能分析
```bash
# 启用内置性能分析
mmkvreader "E:\data" --search "config" --perf
```

### 2. 基准测试
```bash
# 运行内置基准测试
mmkvreader benchmark "E:\data"
```

### 3. 外部分析工具
```bash
# Python 分析脚本
python scripts/performance_analysis.py target/release/mmkvreader.exe E:\data

# Windows 批处理脚本
scripts\performance_analysis.bat target\release\mmkvreader.exe E:\data
```

## 性能分析工具对比

| 工具 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| 内置性能分析 (`--perf`) | 简单易用，无需额外工具 | 信息有限 | 快速检查性能 |
| 内置基准测试 (`benchmark`) | 自动测试多种配置 | 只测试线程数影响 | 找出最佳线程数 |
| Python 分析脚本 | 详细分析，生成图表 | 需要Python环境 | 深入性能分析 |
| Windows 批处理脚本 | 无需额外依赖 | 功能有限 | Windows快速测试 |

## 性能瓶颈检测流程

### 第一步：快速检查
```bash
# 启用性能分析模式
mmkvreader "E:\data" --search "config" --perf --debug
```

查看输出中的性能报告，重点关注：
- 各操作的时间占比
- 是否有明显的瓶颈操作

### 第二步：线程数优化
```bash
# 运行基准测试找出最佳线程数
mmkvreader benchmark "E:\data"
```

观察不同线程数下的性能表现：
- 单线程 vs 多线程的提升
- 线程数增加时的边际效应
- 最佳性能的线程数

### 第三步：深入分析
```bash
# 使用外部工具进行详细分析
python scripts/performance_analysis.py target/release/mmkvreader.exe E:\data
```

生成的分析包括：
- 性能曲线图
- 线程扩展性分析
- 瓶颈识别建议

## 常见性能问题及解决方案

### 1. 文件解压瓶颈 (>50% 时间)
**症状**: 性能报告显示"文件解压"占用大部分时间

**解决方案**:
- 使用SSD存储
- 减少不必要的解压（检查是否已解压）
- 考虑预解压所有文件

```bash
# 预解压所有文件
mmkvreader extract "E:\data"

# 然后搜索时跳过解压步骤
mmkvreader "E:\data" --search "config" --cleanup no
```

### 2. MMKV搜索瓶颈 (>30% 时间)
**症状**: 性能报告显示"MMKV搜索"占用较多时间

**解决方案**:
- 优化搜索关键字（减少不必要的关键字）
- 使用更精确的搜索模式
- 考虑索引优化

```bash
# 使用更精确的搜索
mmkvreader "E:\data" --search "exact_key" --mode key
```

### 3. 线程竞争问题
**症状**: 增加线程数后性能提升不明显或下降

**解决方案**:
- 使用推荐的线程数（CPU核心数-1）
- 检查磁盘I/O是否成为瓶颈
- 考虑使用批处理模式

```bash
# 使用推荐线程数
mmkvreader "E:\data" --search "config" --threads 15  # 假设16核CPU
```

### 4. 内存使用过高
**症状**: 系统内存使用率过高，出现交换

**解决方案**:
- 启用清理模式
- 减少并发线程数
- 分批处理大量文件

```bash
# 启用清理模式减少内存使用
mmkvreader "E:\data" --search "config" --cleanup yes --threads 8
```

## 性能优化最佳实践

### 1. 硬件优化
- **存储**: 使用NVMe SSD，避免机械硬盘
- **内存**: 确保有足够RAM，避免交换
- **CPU**: 多核CPU对并行处理有显著帮助

### 2. 配置优化
```bash
# 推荐的高性能配置
mmkvreader "E:\data" \
  --search "config,user" \
  --mode key \
  --threads 15 \
  --cleanup yes \
  --zip yes
```

### 3. 工作流优化
```bash
# 1. 预解压（一次性操作）
mmkvreader extract "E:\data"

# 2. 多次搜索时不清理
mmkvreader "E:\data" --search "config" --cleanup no
mmkvreader "E:\data" --search "user" --cleanup no

# 3. 最后清理
mmkvreader clean "E:\data"
```

## 性能监控脚本

### 持续监控脚本
```bash
#!/bin/bash
# monitor_performance.sh

echo "开始性能监控..."
while true; do
    echo "$(date): 开始测试"
    
    # 运行性能测试
    time mmkvreader "test_data" --search "config" --perf > perf_log_$(date +%Y%m%d_%H%M%S).txt
    
    # 等待5分钟
    sleep 300
done
```

### 性能回归检测
```bash
#!/bin/bash
# regression_test.sh

BASELINE_TIME=45.0  # 基准时间（秒）
THRESHOLD=1.1       # 允许的性能下降阈值（10%）

# 运行测试
ACTUAL_TIME=$(mmkvreader benchmark "test_data" | grep "平均耗时" | awk '{print $2}' | sed 's/s//')

# 检查性能回归
if (( $(echo "$ACTUAL_TIME > $BASELINE_TIME * $THRESHOLD" | bc -l) )); then
    echo "⚠️ 性能回归检测: 当前 ${ACTUAL_TIME}s > 基准 ${BASELINE_TIME}s * ${THRESHOLD}"
    exit 1
else
    echo "✅ 性能正常: 当前 ${ACTUAL_TIME}s"
fi
```

## 高级性能分析

### 使用系统工具
```bash
# Linux: 使用 perf 进行CPU分析
perf record -g ./target/release/mmkvreader "data" --search "config"
perf report

# Windows: 使用 WPA 进行详细分析
wpr -start CPU -start FileIO
mmkvreader "data" --search "config"
wpr -stop performance.etl
wpa performance.etl
```

### 内存分析
```bash
# 使用 heaptrack 分析内存使用
heaptrack ./target/release/mmkvreader "data" --search "config"
heaptrack_gui heaptrack.mmkvreader.*.gz
```

### 火焰图生成
```bash
# 生成CPU火焰图
cargo install flamegraph
cargo flamegraph --bin mmkvreader -- "data" --search "config"
```

## 总结

通过这些工具和方法，你可以：

1. **快速识别**性能瓶颈
2. **优化配置**获得最佳性能
3. **监控性能**防止回归
4. **深入分析**复杂的性能问题

记住，性能优化是一个迭代过程，需要根据具体的使用场景和数据特点进行调整。
