@echo off
REM 简化版 MMKV Reader 性能测试脚本
REM 用法: simple_benchmark.bat <mmkv_binary_path> <test_data_dir>

setlocal enabledelayedexpansion

if "%~2"=="" (
    echo 用法: %0 ^<mmkv_binary_path^> ^<test_data_dir^>
    echo 示例: %0 target\release\mmkvreader.exe E:\test_data
    exit /b 1
)

set "BINARY_PATH=%~1"
set "TEST_DATA_DIR=%~2"
set "SEARCH_TERMS=config,user,token"

echo ========================================
echo MMKV Reader 简化性能测试
echo ========================================
echo 可执行文件: %BINARY_PATH%
echo 测试数据: %TEST_DATA_DIR%
echo 搜索关键字: %SEARCH_TERMS%
echo ========================================
echo.

REM 检查文件是否存在
if not exist "%BINARY_PATH%" (
    echo 错误: 可执行文件不存在: %BINARY_PATH%
    pause
    exit /b 1
)

if not exist "%TEST_DATA_DIR%" (
    echo 错误: 测试数据目录不存在: %TEST_DATA_DIR%
    pause
    exit /b 1
)

REM 获取CPU核心数
for /f "skip=1" %%i in ('wmic cpu get NumberOfCores') do (
    if not "%%i"=="" (
        set "CPU_CORES=%%i"
        goto :got_cores
    )
)
:got_cores

echo CPU核心数: %CPU_CORES%
echo.

REM 测试不同线程数
echo 开始性能测试...
echo.

REM 测试1: 单线程
echo [1/5] 测试单线程性能...
echo 开始时间: %time%
"%BINARY_PATH%" "%TEST_DATA_DIR%" --search "%SEARCH_TERMS%" --threads 1 --cleanup yes --zip no
echo 结束时间: %time%
echo.

REM 测试2: 4线程
echo [2/5] 测试4线程性能...
echo 开始时间: %time%
"%BINARY_PATH%" "%TEST_DATA_DIR%" --search "%SEARCH_TERMS%" --threads 4 --cleanup yes --zip no
echo 结束时间: %time%
echo.

REM 测试3: 8线程
echo [3/5] 测试8线程性能...
echo 开始时间: %time%
"%BINARY_PATH%" "%TEST_DATA_DIR%" --search "%SEARCH_TERMS%" --threads 8 --cleanup yes --zip no
echo 结束时间: %time%
echo.

REM 测试4: CPU核心数线程
echo [4/5] 测试%CPU_CORES%线程性能...
echo 开始时间: %time%
"%BINARY_PATH%" "%TEST_DATA_DIR%" --search "%SEARCH_TERMS%" --threads %CPU_CORES% --cleanup yes --zip no
echo 结束时间: %time%
echo.

REM 测试5: 内置基准测试
echo [5/5] 运行内置基准测试...
echo 开始时间: %time%
"%BINARY_PATH%" benchmark "%TEST_DATA_DIR%"
echo 结束时间: %time%
echo.

echo ========================================
echo 性能测试完成!
echo ========================================
echo.
echo 性能优化建议:
echo 1. 观察上述测试结果，选择性能最佳的线程数
echo 2. 使用SSD存储可显著提升I/O性能
echo 3. 确保有足够的内存避免交换文件
echo 4. 对于大量文件，适中的线程数通常比极高线程数更有效
echo.

REM 生成报告
echo 生成测试报告...
echo MMKV Reader 性能测试报告 > benchmark_report.txt
echo ======================================== >> benchmark_report.txt
echo 测试时间: %date% %time% >> benchmark_report.txt
echo 可执行文件: %BINARY_PATH% >> benchmark_report.txt
echo 测试数据: %TEST_DATA_DIR% >> benchmark_report.txt
echo CPU核心数: %CPU_CORES% >> benchmark_report.txt
echo 搜索关键字: %SEARCH_TERMS% >> benchmark_report.txt
echo ======================================== >> benchmark_report.txt
echo. >> benchmark_report.txt
echo 详细测试结果请查看控制台输出 >> benchmark_report.txt
echo 建议使用内置基准测试获得更准确的性能数据 >> benchmark_report.txt

echo 测试报告已保存到: benchmark_report.txt
echo.

pause
