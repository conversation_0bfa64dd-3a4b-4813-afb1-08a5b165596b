# MMKV 批量搜索功能

这个工具提供了高性能的 MMKV 批量搜索功能，可以处理大量的 tgz 文件并在其中搜索 MMKV 数据。

## 功能特点

- 🚀 **高性能并行处理** - 使用 Rayon 并行处理多个文件，智能线程数控制
- 📦 **自动解压** - 自动解压 tgz 文件到临时目录
- 🔍 **智能搜索** - 支持在 key 或 value 中搜索，支持模糊搜索
- 🔐 **加密支持** - 支持加密的 MMKV 文件
- 📊 **详细统计** - 提供处理进度和性能统计
- 💾 **结果保存** - 自动保存搜索结果到 JSON 文件
- 🧹 **自动清理** - 处理完成后自动清理临时文件
- 🔄 **多关键字搜索** - 一次搜索多个关键字，优化性能
- 📄 **单文件支持** - 支持处理单个 tgz 文件
- 🌐 **全量导出** - 支持导出所有 key-value 数据
- 🐛 **调试模式** - 详细的调试信息输出
- ⚙️ **子命令支持** - 清理、压缩、解压、测试等独立功能

## 使用方法

### 1. 命令行使用

```bash
# 全量导出模式（导出所有key-value）
mmkvreader "E:\path\to\tgz\files"

# 基本搜索模式
mmkvreader "E:\path\to\tgz\files" --search "config"

# 多关键字搜索
mmkvreader "E:\path\to\tgz\files" --search "config,camera,user"

# 在value中搜索
mmkvreader "E:\path\to\tgz\files" --search "config" --mode value

# 单文件处理
mmkvreader "E:\path\to\file.tgz" --search "config"

# 调试模式
mmkvreader "E:\path\to\tgz\files" --search "config" --debug

# 子命令
mmkvreader clean "E:\path\to\tgz\files"
mmkvreader zip "E:\path\to\tgz\files"
mmkvreader extract "E:\path\to\tgz\files"
mmkvreader test
```

### 2. Rust API 使用

```rust
use mmkvreader::Mmkv;

// 多关键字搜索
let directory = "E:\\path\\to\\tgz\\files";
let crypt_key = Some("your_encryption_key");
let search_keywords = vec!["config".to_string(), "camera".to_string()];
let search_mode = 1; // 1=搜索key, 2=搜索value
let threads = 8;
let should_cleanup = false;
let debug = false;

// 执行多关键字搜索
match Mmkv::search_in_tgz_directory_multi_keywords(
    directory,
    crypt_key,
    &search_keywords,
    search_mode,
    threads,
    should_cleanup,
    None, // search_dir
    debug,
) {
    Ok(stats) => {
        println!("搜索完成! 处理了 {} 个文件，找到 {} 个匹配",
                 stats.processed_files, stats.total_matches);
    }
    Err(e) => {
        println!("搜索失败: {}", e);
    }
}

// 单文件处理
let tgz_path = std::path::Path::new("E:\\path\\to\\file.tgz");
match Mmkv::process_single_tgz_file_multi_keywords(
    tgz_path,
    crypt_key,
    &search_keywords,
    search_mode.into(),
    should_cleanup,
    None, // search_dir
    debug,
) {
    Ok(result) => {
        println!("单文件处理完成! 找到 {} 个匹配", result.total_matches);
    }
    Err(e) => {
        println!("单文件处理失败: {}", e);
    }
}
```

### 3. 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| `directory` | `&str` | 包含 tgz 文件的目录路径 |
| `crypt_key` | `Option<&str>` | MMKV 加密密钥，如果文件未加密则传 `None` |
| `search_keywords` | `&[String]` | 要搜索的关键字列表，空数组表示全量导出 |
| `search_mode` | `i32` | 搜索模式：`1` = 搜索 key，`2` = 搜索 value |
| `threads` | `usize` | 并行线程数，建议使用 CPU 核心数-1 |
| `should_cleanup` | `bool` | 是否清理临时文件 |
| `debug` | `bool` | 是否启用调试模式 |

## 搜索模式

### 1. 全量导出模式
不指定搜索关键字时，导出所有 key-value 数据。

```bash
mmkvreader "E:\path\to\tgz\files"  # 导出所有数据
```

### 2. 搜索 Key 模式 (search_mode = 1)
在所有 MMKV 文件的 key 名称中搜索指定内容，支持模糊搜索（不区分大小写）。

```bash
mmkvreader "E:\path\to\tgz\files" --search "config" --mode key
```

### 3. 搜索 Value 模式 (search_mode = 2)
在所有 MMKV 文件的 value 内容中搜索指定内容，支持模糊搜索（不区分大小写）。

```bash
mmkvreader "E:\path\to\tgz\files" --search "camera" --mode value
```

### 4. 多关键字搜索
支持同时搜索多个关键字，使用多种分隔符：

```bash
# 支持的分隔符：中文逗号、英文逗号、空格、竖杠
mmkvreader "E:\path\to\tgz\files" --search "config,camera|user data"
```

## 输出结果

### 1. 控制台输出
程序会实时显示处理进度：

```
🔍 MMKV 批量搜索工具
==================================================
📁 目录: E:\test_tgz_files
🔑 加密密钥: fd9f5bef68c54a1ecf70757a6d6f565b
🔍 搜索关键字: ["config", "camera"]
📋 搜索模式: Value
📦 压缩结果: 是
🧵 线程数: 15 (CPU数: 16)
🧹 清理临时文件: 否
🐛 调试模式: 关闭

🚀 使用优化的多关键字搜索模式
🔍 关键字列表: ["config", "camera"]
📦 找到 1500 个 tgz 文件
⏳ 进度: 100/1500 (6.7%), 速度: 12.5 文件/秒, 总匹配: 45
⏳ 进度: 200/1500 (13.3%), 速度: 15.2 文件/秒, 总匹配: 89
...

✅ 多关键字搜索完成: 处理 1500 文件，找到 234 匹配

==================================================
🎉 所有搜索任务完成!
📊 总体统计:
   搜索关键字: 2 个
   总匹配数: 234
   总耗时: 98.45 秒
📦 结果已压缩到: E:\test_tgz_files\ck_search_results_20241224_143022.zip
```

### 2. 调试模式输出
启用调试模式时会显示详细信息：

```
🐛 [DEBUG] 文件: user_config | all_keys数量: 156 | 搜索关键字: ["config", "camera"]
🐛 [DEBUG] 前10个keys: ["app_version", "user_id", "camera_settings", "config_version", ...]
🐛 [DEBUG] 文件: user_config | 搜索结果数量: 3
```

### 3. JSON 结果文件
每个 tgz 文件的搜索结果会保存为同名的 JSON 文件：

```json
{
  "tgz_file": "E:\\test\\example.tgz",
  "processed_at": "2024-01-15T10:30:45Z",
  "total_mmkv_files": 3,
  "total_matches": 2,
  "processing_time_ms": 1250,
  "matches": [
    {
      "mmkv_file": "data/user_config",
      "mmkv_id": "user_config",
      "key": "camera_settings",
      "value": "{\"resolution\":\"1920x1080\",\"fps\":30}",
      "value_type": "string",
      "match_type": "value",
      "matched_keywords": ["camera", "settings"]
    }
  ],
  "errors": []
}
```

### 4. ZIP 压缩文件
默认会将所有 JSON 结果压缩为一个 ZIP 文件：

- **文件名格式**: `ck_search_results_<时间戳>.zip`
- **包含内容**: 所有匹配的 JSON 文件 + 搜索摘要文件
- **摘要文件**: `search_summary.txt` 包含搜索参数和统计信息

## 性能优化

### 1. 并行处理
- 使用 Rayon 库进行并行处理
- 智能线程数控制：默认 CPU 核心数-1，最大 1024
- 支持处理超过 20,000 个文件

### 2. 搜索优化
- 一次文件打开搜索所有关键字，减少 I/O 开销
- 模糊搜索支持，不区分大小写
- 并行字符串匹配，串行 MMKV 访问

### 3. 内存管理
- 及时清理解压的临时文件
- 流式处理避免内存溢出
- 智能批处理减少 I/O 开销

### 4. 错误处理
- 单个文件失败不影响整体处理
- 详细的错误日志记录
- 调试模式提供详细诊断信息

### 5. 文件管理
- 简化命名：解压目录和 JSON 文件直接使用 tgz 文件名
- 智能清理：只清理与 tgz 文件对应的临时文件
- 文件过滤：自动跳过 `.crc`、`.log`、`.tmp` 等非 MMKV 文件

## 注意事项

1. **磁盘空间**: 确保有足够的磁盘空间用于临时解压文件
2. **权限**: 确保对目标目录有读写权限
3. **加密密钥**: 如果 MMKV 文件加密，必须提供正确的密钥
4. **文件格式**: 仅支持 `.tgz` 和 `.tar.gz` 格式的压缩文件
5. **线程数**: 建议使用默认线程数（CPU 核心数-1），避免过度并发
6. **调试模式**: 调试模式会增加输出量，可能影响处理速度
7. **清理选项**: 默认不清理临时文件，便于调试和结果验证

## 示例场景

### 场景 1: 全量数据导出
```bash
# 导出所有 key-value 数据
mmkvreader "E:\logs\2024"
```

### 场景 2: 查找配置相关的数据
```bash
# 在 value 中搜索配置相关内容
mmkvreader "E:\logs\2024" --search "config,settings,preference" --mode value
```

### 场景 3: 查找特定的 key
```bash
# 在 key 中搜索用户相关的键
mmkvreader "E:\backup\mmkv" --search "user_id,user_name" --mode key
```

### 场景 4: 单文件调试
```bash
# 处理单个文件并启用调试模式
mmkvreader "E:\test\sample.tgz" --search "debug" --debug
```

### 场景 5: 大批量处理
```bash
# 处理包含 20,000+ 个 tgz 文件的目录，使用自定义线程数
mmkvreader "E:\massive_data" --search "error,exception" --threads 32 --cleanup yes
```

### 场景 6: 子命令使用
```bash
# 预先解压所有文件
mmkvreader extract "E:\data"

# 执行搜索
mmkvreader "E:\data" --search "token" --zip no

# 手动压缩结果
mmkvreader zip "E:\data"

# 清理临时文件
mmkvreader clean "E:\data"
```

## 故障排除

### 常见问题

1. **路径不存在**
   ```
   ❌ 错误: 路径不存在: E:\path\to\files
   ```
   解决：检查目录或文件路径是否正确

2. **没有找到 tgz 文件**
   ```
   ❌ 目录中没有找到 tgz 文件
   ```
   解决：确认目录中包含 .tgz 或 .tar.gz 文件

3. **加密密钥错误**
   ```
   ❌ 无法加载 MMKV 文件: 解密失败
   ```
   解决：检查提供的加密密钥是否正确

4. **权限问题**
   ```
   ❌ 创建解压目录失败: 权限被拒绝
   ```
   解决：确保对目标目录有写权限

5. **输入路径类型错误**
   ```
   ❌ 错误: 输入路径必须是目录或.tgz文件: E:\path\to\file.txt
   ```
   解决：确保输入路径是目录或 .tgz 文件

6. **调试信息过多**
   ```
   🐛 [DEBUG] 文件: xxx | all_keys数量: 1000+ | ...
   ```
   解决：关闭调试模式（移除 `--debug` 参数）以提高处理速度

### 性能调优建议

1. **线程数设置**: 使用默认值（CPU 核心数-1）通常是最优的
2. **清理策略**: 处理大量文件时建议启用 `--cleanup yes` 节省磁盘空间
3. **调试模式**: 仅在需要排查问题时启用，正常使用时关闭
4. **压缩选项**: 大量结果文件建议启用压缩（默认启用）
