# MMKV 批量搜索功能

这个工具提供了高性能的 MMKV 批量搜索功能，可以处理大量的 tgz 文件并在其中搜索 MMKV 数据。

## 功能特点

- 🚀 **高性能并行处理** - 使用 Rayon 并行处理多个文件
- 📦 **自动解压** - 自动解压 tgz 文件到临时目录
- 🔍 **智能搜索** - 支持在 key 或 value 中搜索
- 🔐 **加密支持** - 支持加密的 MMKV 文件
- 📊 **详细统计** - 提供处理进度和性能统计
- 💾 **结果保存** - 自动保存搜索结果到 JSON 文件
- 🧹 **自动清理** - 处理完成后自动清理临时文件

## 使用方法

### 1. 基本用法

```rust
use mmkvreader::Mmkv;

// 搜索参数
let directory = "E:\\path\\to\\tgz\\files";  // 包含 tgz 文件的目录
let crypt_key = Some("your_encryption_key"); // 加密密钥（可选）
let search_term = "config";                  // 搜索内容
let search_mode = 2;                         // 1=搜索key, 2=搜索value

// 执行搜索
match Mmkv::search_in_tgz_directory(directory, crypt_key, search_term, search_mode) {
    Ok(stats) => {
        println!("搜索完成! 处理了 {} 个文件，找到 {} 个匹配", 
                 stats.processed_files, stats.total_matches);
    }
    Err(e) => {
        println!("搜索失败: {}", e);
    }
}
```

### 2. 命令行使用

```bash
# 默认模式：读取单个 MMKV 实例
cargo run

# 搜索模式：批量处理 tgz 文件
cargo run search
```

### 3. 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| `directory` | `&str` | 包含 tgz 文件的目录路径 |
| `crypt_key` | `Option<&str>` | MMKV 加密密钥，如果文件未加密则传 `None` |
| `search_term` | `&str` | 要搜索的内容 |
| `search_mode` | `i32` | 搜索模式：`1` = 搜索 key，`2` = 搜索 value |

## 搜索模式

### 1. 搜索 Key (search_mode = 1)
在所有 MMKV 文件的 key 名称中搜索指定内容。

```rust
let search_mode = 1;  // 搜索 key
let search_term = "config";  // 查找包含 "config" 的 key
```

### 2. 搜索 Value (search_mode = 2)
在所有 MMKV 文件的 value 内容中搜索指定内容。

```rust
let search_mode = 2;  // 搜索 value
let search_term = "camera";  // 查找包含 "camera" 的 value
```

## 输出结果

### 1. 控制台输出
程序会实时显示处理进度：

```
🔍 开始搜索任务
📁 目录: E:\test_tgz_files
🔑 加密密钥: fd9f5bef68c54a1ecf70757a6d6f565b
🔍 搜索内容: config
📋 搜索模式: Value
📦 找到 1500 个 tgz 文件

⏳ 进度: 100/1500 (6.7%), 速度: 12.5 文件/秒, 总匹配: 45
⏳ 进度: 200/1500 (13.3%), 速度: 15.2 文件/秒, 总匹配: 89
...

✅ 搜索完成!
📊 统计信息:
   总文件数: 1500
   成功处理: 1485
   失败文件: 15
   总匹配数: 234
   总耗时: 98.45 秒
   平均速度: 15.1 文件/秒
```

### 2. JSON 结果文件
每个 tgz 文件的搜索结果会保存为同名的 JSON 文件：

```json
{
  "tgz_file": "E:\\test\\example.tgz",
  "processed_at": "2024-01-15T10:30:45Z",
  "total_mmkv_files": 3,
  "total_matches": 2,
  "processing_time_ms": 1250,
  "matches": [
    {
      "mmkv_file": "data/user_config",
      "mmkv_id": "user_config",
      "key": "camera_settings",
      "value": "{\"resolution\":\"1920x1080\",\"fps\":30}",
      "value_type": "string",
      "match_type": "value"
    }
  ],
  "errors": []
}
```

## 性能优化

### 1. 并行处理
- 使用 Rayon 库进行并行处理
- 自动根据 CPU 核心数调整并发数
- 支持处理超过 20,000 个文件

### 2. 内存管理
- 及时清理解压的临时文件
- 流式处理避免内存溢出
- 智能批处理减少 I/O 开销

### 3. 错误处理
- 单个文件失败不影响整体处理
- 详细的错误日志记录
- 自动重试机制

## 注意事项

1. **磁盘空间**: 确保有足够的磁盘空间用于临时解压文件
2. **权限**: 确保对目标目录有读写权限
3. **加密密钥**: 如果 MMKV 文件加密，必须提供正确的密钥
4. **文件格式**: 仅支持 `.tgz` 和 `.tar.gz` 格式的压缩文件

## 示例场景

### 场景 1: 查找配置相关的数据
```rust
Mmkv::search_in_tgz_directory(
    "E:\\logs\\2024\\",
    Some("encryption_key"),
    "config",
    2  // 在 value 中搜索
);
```

### 场景 2: 查找特定的 key
```rust
Mmkv::search_in_tgz_directory(
    "E:\\backup\\mmkv\\",
    None,  // 无加密
    "user_id",
    1  // 在 key 中搜索
);
```

### 场景 3: 大批量处理
```rust
// 处理包含 20,000+ 个 tgz 文件的目录
Mmkv::search_in_tgz_directory(
    "E:\\massive_data\\",
    Some("secret_key"),
    "error",
    2
);
```

## 故障排除

### 常见问题

1. **目录不存在**
   ```
   ❌ 目录不存在: E:\path\to\files
   ```
   解决：检查目录路径是否正确

2. **没有找到 tgz 文件**
   ```
   ❌ 目录中没有找到 tgz 文件
   ```
   解决：确认目录中包含 .tgz 或 .tar.gz 文件

3. **加密密钥错误**
   ```
   ❌ 无法加载 MMKV 文件: 解密失败
   ```
   解决：检查提供的加密密钥是否正确

4. **权限问题**
   ```
   ❌ 创建解压目录失败: 权限被拒绝
   ```
   解决：确保对目标目录有写权限
