use std::env;
use std::path::PathBuf;

fn main() {
    // 编译 C++ wrapper
    let mut build = cc::Build::new();
    build.cpp(true)
        .file("src/mmkv_c_wrapper.cpp")
        .include("include")
        .include("include/MMKV");

    // Add Windows-specific flags
    if cfg!(target_os = "windows") {
        build.flag("/std:c++17")
             .flag("/MT");  // Use static runtime to match mmkv.lib
    }

    build.compile("mmkv_c_wrapper");

    // 告诉 cargo 链接 mmkv.lib
    println!("cargo:rustc-link-search=lib");
    println!("cargo:rustc-link-lib=mmkv");

    // 设置 bindgen
    let bindings = bindgen::Builder::default()
        .header("include/mmkv_c_wrapper.h")
        .parse_callbacks(Box::new(bindgen::CargoCallbacks::new()))
        .generate()
        .expect("Unable to generate bindings");

    // 写入输出文件
    let out_path = PathBuf::from(env::var("OUT_DIR").unwrap());
    bindings
        .write_to_file(out_path.join("bindings.rs"))
        .expect("Couldn't write bindings!");
}