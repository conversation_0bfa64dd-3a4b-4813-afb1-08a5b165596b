# MMKV Reader 性能分析脚本 (PowerShell)
# 用法: .\performance_analysis.ps1 -BinaryPath "target\release\mmkvreader.exe" -TestDataDir "E:\data"

param(
    [Parameter(Mandatory=$true)]
    [string]$BinaryPath,
    
    [Parameter(Mandatory=$true)]
    [string]$TestDataDir,
    
    [string]$SearchTerms = "config,user,token",
    [int]$Rounds = 3,
    [switch]$Detailed
)

# 检查参数
if (-not (Test-Path $BinaryPath)) {
    Write-Error "可执行文件不存在: $BinaryPath"
    exit 1
}

if (-not (Test-Path $TestDataDir)) {
    Write-Error "测试数据目录不存在: $TestDataDir"
    exit 1
}

# 获取系统信息
$cpuCores = (Get-WmiObject -Class Win32_ComputerSystem).NumberOfLogicalProcessors
$totalMemory = [math]::Round((Get-WmiObject -Class Win32_ComputerSystem).TotalPhysicalMemory / 1GB, 2)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "MMKV Reader 性能分析" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "可执行文件: $BinaryPath"
Write-Host "测试数据: $TestDataDir"
Write-Host "搜索关键字: $SearchTerms"
Write-Host "测试轮数: $Rounds"
Write-Host "CPU核心数: $cpuCores"
Write-Host "总内存: ${totalMemory}GB"
Write-Host ""

# 测试配置
$threadConfigs = @(
    @{Name="单线程"; Threads=1},
    @{Name="双线程"; Threads=2},
    @{Name="四线程"; Threads=4},
    @{Name="八线程"; Threads=8},
    @{Name="CPU数-1"; Threads=($cpuCores-1)},
    @{Name="CPU数"; Threads=$cpuCores},
    @{Name="CPU数*2"; Threads=($cpuCores*2)}
)

$results = @()

foreach ($config in $threadConfigs) {
    Write-Host "🧪 测试配置: $($config.Name) ($($config.Threads) 线程)" -ForegroundColor Yellow
    Write-Host "----------------------------------------"
    
    $roundResults = @()
    
    for ($round = 1; $round -le $Rounds; $round++) {
        Write-Host "  轮次 $round/$Rounds" -NoNewline
        
        # 记录开始时间
        $startTime = Get-Date
        
        # 构建命令
        $arguments = @(
            "`"$TestDataDir`"",
            "--search", "`"$SearchTerms`"",
            "--threads", $config.Threads,
            "--cleanup", "yes",
            "--zip", "no"
        )
        
        try {
            # 运行测试
            $process = Start-Process -FilePath $BinaryPath -ArgumentList $arguments -Wait -PassThru -NoNewWindow -RedirectStandardOutput "temp_output_$($config.Threads)_$round.txt" -RedirectStandardError "temp_error_$($config.Threads)_$round.txt"
            
            $endTime = Get-Date
            $elapsed = ($endTime - $startTime).TotalSeconds
            
            if ($process.ExitCode -eq 0) {
                # 解析输出
                $output = Get-Content "temp_output_$($config.Threads)_$round.txt" -Raw
                
                # 提取统计信息
                $processedFiles = 0
                $totalMatches = 0
                
                if ($output -match "处理 (\d+) 文件.*找到 (\d+) 匹配") {
                    $processedFiles = [int]$matches[1]
                    $totalMatches = [int]$matches[2]
                }
                
                $filesPerSecond = if ($elapsed -gt 0) { [math]::Round($processedFiles / $elapsed, 2) } else { 0 }
                
                $roundResults += @{
                    Round = $round
                    Elapsed = $elapsed
                    ProcessedFiles = $processedFiles
                    TotalMatches = $totalMatches
                    FilesPerSecond = $filesPerSecond
                    Success = $true
                }
                
                Write-Host " - 成功 (${elapsed}s, ${processedFiles}文件, ${filesPerSecond}文件/秒)" -ForegroundColor Green
            } else {
                $errorOutput = Get-Content "temp_error_$($config.Threads)_$round.txt" -Raw
                $roundResults += @{
                    Round = $round
                    Elapsed = $elapsed
                    ProcessedFiles = 0
                    TotalMatches = 0
                    FilesPerSecond = 0
                    Success = $false
                    Error = $errorOutput
                }
                
                Write-Host " - 失败 (退出代码: $($process.ExitCode))" -ForegroundColor Red
                if ($Detailed) {
                    Write-Host "    错误: $errorOutput" -ForegroundColor Red
                }
            }
        }
        catch {
            Write-Host " - 异常: $($_.Exception.Message)" -ForegroundColor Red
            $roundResults += @{
                Round = $round
                Elapsed = 0
                ProcessedFiles = 0
                TotalMatches = 0
                FilesPerSecond = 0
                Success = $false
                Error = $_.Exception.Message
            }
        }
        
        # 清理临时文件
        Remove-Item "temp_output_$($config.Threads)_$round.txt" -ErrorAction SilentlyContinue
        Remove-Item "temp_error_$($config.Threads)_$round.txt" -ErrorAction SilentlyContinue
    }
    
    # 计算平均值
    $successfulRounds = $roundResults | Where-Object { $_.Success }
    if ($successfulRounds.Count -gt 0) {
        $avgElapsed = ($successfulRounds | Measure-Object -Property Elapsed -Average).Average
        $avgFilesPerSecond = ($successfulRounds | Measure-Object -Property FilesPerSecond -Average).Average
        $avgProcessedFiles = ($successfulRounds | Measure-Object -Property ProcessedFiles -Average).Average
        $avgMatches = ($successfulRounds | Measure-Object -Property TotalMatches -Average).Average
        
        Write-Host "  📊 平均结果:" -ForegroundColor Cyan
        Write-Host "    平均耗时: $([math]::Round($avgElapsed, 2))s"
        Write-Host "    平均速度: $([math]::Round($avgFilesPerSecond, 2)) 文件/秒"
        Write-Host "    平均文件数: $([math]::Round($avgProcessedFiles, 0))"
        Write-Host "    平均匹配数: $([math]::Round($avgMatches, 0))"
        
        $results += @{
            ConfigName = $config.Name
            Threads = $config.Threads
            AvgElapsed = $avgElapsed
            AvgFilesPerSecond = $avgFilesPerSecond
            AvgProcessedFiles = $avgProcessedFiles
            AvgMatches = $avgMatches
            SuccessfulRounds = $successfulRounds.Count
            TotalRounds = $Rounds
        }
    } else {
        Write-Host "  ❌ 所有轮次都失败了" -ForegroundColor Red
    }
    
    Write-Host ""
}

# 分析结果
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "性能分析结果" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

if ($results.Count -gt 0) {
    # 找出最佳配置
    $bestConfig = $results | Sort-Object -Property AvgFilesPerSecond -Descending | Select-Object -First 1
    
    Write-Host "🏆 最佳配置: $($bestConfig.ConfigName) ($($bestConfig.Threads) 线程)" -ForegroundColor Green
    Write-Host "   平均速度: $([math]::Round($bestConfig.AvgFilesPerSecond, 2)) 文件/秒"
    Write-Host "   平均耗时: $([math]::Round($bestConfig.AvgElapsed, 2)) 秒"
    Write-Host ""
    
    # 显示所有结果
    Write-Host "详细结果:" -ForegroundColor Yellow
    $results | Sort-Object -Property AvgFilesPerSecond -Descending | ForEach-Object {
        $efficiency = if ($_.Threads -gt 1) { 
            $singleThreadSpeed = ($results | Where-Object { $_.Threads -eq 1 }).AvgFilesPerSecond
            if ($singleThreadSpeed -gt 0) { 
                [math]::Round(($_.AvgFilesPerSecond / $singleThreadSpeed / $_.Threads) * 100, 1) 
            } else { 0 }
        } else { 100 }
        
        Write-Host "  $($_.ConfigName): $([math]::Round($_.AvgFilesPerSecond, 2)) 文件/秒 (效率: ${efficiency}%)"
    }
    
    # 生成报告
    $reportPath = "performance_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
    $results | ConvertTo-Json -Depth 3 | Out-File -FilePath $reportPath -Encoding UTF8
    
    Write-Host ""
    Write-Host "📄 详细报告已保存到: $reportPath" -ForegroundColor Green
} else {
    Write-Host "❌ 没有成功的测试结果" -ForegroundColor Red
}

Write-Host ""
Write-Host "💡 性能优化建议:" -ForegroundColor Yellow
Write-Host "  - 使用SSD存储可显著提升I/O性能"
Write-Host "  - 根据测试结果选择最佳线程数"
Write-Host "  - 确保有足够内存避免交换文件"
Write-Host "  - 对于大量小文件，适中线程数通常更有效"
